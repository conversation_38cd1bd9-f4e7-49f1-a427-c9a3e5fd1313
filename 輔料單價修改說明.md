# 輔料單價修改說明

## 修改內容

### 1. 修復供應商代碼沒抓到的問題

**問題**：原本供應商列表只從輔料單價記錄中獲取，導致新增時沒有供應商可選。

**解決方案**：
- 修改後端 `backend/routers/auxiliary_pricing.py` 中的 `get_suppliers_list()` 函數
- 改為從供應商管理表 (`Supplier`) 中獲取活躍的供應商
- 返回完整的供應商資訊，包括：
  - `supplier_code`: 供應商代碼
  - `company_name`: 公司名稱
  - `supplier_type`: 供應商類型
  - `display_name`: 顯示名稱（代碼 - 公司名稱）

**前端修改**：
- 更新 `frontend/src/pages/AuxiliaryPricing.tsx` 中的供應商選擇組件
- 支援搜尋和篩選功能
- 顯示格式：`供應商代碼 - 公司名稱`

### 2. 新增商品分類

**新增的預設商品分類**：
- 膠帶類
- 包裝材料
- 印刷耗材
- 機械配件
- 辦公用品
- 清潔用品
- 安全防護
- 其他輔料

**實作方式**：
- 修改 `get_categories_list()` 函數
- 合併預設分類和資料庫中已使用的分類
- 自動排序並去重

### 3. 新增單位選項

**新增的單位**：
- `L` (升)
- `cuộn(卷)` (卷)

**完整的單位列表**：
- PCS
- KG
- M
- M²
- SET
- BOX
- L
- cuộn(卷)

**實作方式**：
- 修改 `get_units_list()` 函數
- 按照預設順序排序
- 合併預設單位和資料庫中已使用的單位

## 修改的檔案

### 後端檔案
1. `backend/routers/auxiliary_pricing.py`
   - 新增 `Supplier` 模型導入
   - 修改 `get_suppliers_list()` 函數
   - 修改 `get_categories_list()` 函數
   - 修改 `get_units_list()` 函數

### 前端檔案
1. `frontend/src/pages/AuxiliaryPricing.tsx`
   - 更新供應商選擇組件（新增表單和篩選區域）
   - 修改供應商資料類型
   - 優化搜尋和篩選功能

## 測試

創建了測試腳本 `test_auxiliary_pricing_changes.py` 來驗證：
- 供應商列表API是否正確返回供應商資訊
- 商品分類是否包含所有預設分類
- 單位列表是否包含新增的單位
- 概況統計API是否正常運作

## 使用方式

1. 啟動後端服務
2. 進入輔料單價管理頁面
3. 點擊「新增輔料單價」
4. 現在可以：
   - 從下拉選單選擇供應商（顯示代碼和公司名稱）
   - 選擇預設的商品分類或輸入自訂分類
   - 選擇包含新增單位的完整單位列表

## 注意事項

- 供應商列表只顯示活躍狀態的供應商
- 商品分類和單位支援自訂輸入，會自動加入到選項中
- 篩選功能支援搜尋供應商代碼和公司名稱
