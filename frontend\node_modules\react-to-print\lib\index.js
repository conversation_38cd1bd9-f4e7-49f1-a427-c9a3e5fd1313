!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("react")):"function"==typeof define&&define.amd?define("lib",["react"],t):"object"==typeof exports?exports.lib=t(require("react")):e.lib=t(e.react)}("undefined"!=typeof self?self:this,function(e){return function(){"use strict";var t={155:function(t){t.exports=e}},o={};function n(e){var r=o[e];if(void 0!==r)return r.exports;var s=o[e]={exports:{}};return t[e](s,s.exports,n),s.exports}n.d=function(e,t){for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{useReactToPrint:function(){return f}});var s=n(155);function i({level:e="error",messages:t,suppressErrors:o=!1}){o||("error"===e?console.error(t):"warning"===e?console.warn(t):console.debug(t))}function l(e,t){if(t||!e){const e=document.getElementById("printWindow");e&&document.body.removeChild(e)}}function a(e){return e instanceof Error?e:new Error("Unknown Error")}function c(e,t){const{documentTitle:o,onAfterPrint:n,onPrintError:r,preserveAfterPrint:s,print:c,suppressErrors:d}=t;setTimeout(()=>{var t,u;if(e.contentWindow){function p(){null==n||n(),l(s)}if(e.contentWindow.focus(),c)c(e).then(p).catch(e=>{r?r("print",a(e)):i({messages:["An error was thrown by the specified `print` function"],suppressErrors:d})});else{if(e.contentWindow.print){const h=null!==(u=null===(t=e.contentDocument)||void 0===t?void 0:t.title)&&void 0!==u?u:"",f=e.ownerDocument.title;o&&(e.ownerDocument.title=o,e.contentDocument&&(e.contentDocument.title=o)),e.contentWindow.print(),o&&(e.ownerDocument.title=f,e.contentDocument&&(e.contentDocument.title=h))}else i({messages:["Printing for this browser is not currently possible: the browser does not have a `print` method available for iframes."],suppressErrors:d});[/Android/i,/webOS/i,/iPhone/i,/iPad/i,/iPod/i,/BlackBerry/i,/Windows Phone/i].some(e=>{var t,o;return(null!==(o=null!==(t=navigator.userAgent)&&void 0!==t?t:navigator.vendor)&&void 0!==o?o:"opera"in window&&window.opera).match(e)})?setTimeout(p,500):p()}}else i({messages:["Printing failed because the `contentWindow` of the print iframe did not load. This is possibly an error with `react-to-print`. Please file an issue: https://github.com/MatthewHerbst/react-to-print/issues/"],suppressErrors:d})},500)}function d(e){const t=[],o=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,null);let n=o.nextNode();for(;n;)t.push(n),n=o.nextNode();return t}function u(e,t,o){const n=d(e),r=d(t);if(n.length===r.length)for(let e=0;e<n.length;e++){const t=n[e],s=r[e],i=t.shadowRoot;if(null!==i){const e=s.attachShadow({mode:i.mode});e.innerHTML=i.innerHTML,u(i,e,o)}}else i({messages:["When cloning shadow root content, source and target elements have different size. `onBeforePrint` likely resolved too early.",e,t],suppressErrors:o})}const p='\n    @page {\n        /* Remove browser default header (title) and footer (url) */\n        margin: 0;\n    }\n    @media print {\n        body {\n            /* Tell browsers to print background colors */\n            color-adjust: exact; /* Firefox. This is an older version of "print-color-adjust" */\n            print-color-adjust: exact; /* Firefox/Safari */\n            -webkit-print-color-adjust: exact; /* Chrome/Safari/Edge/Opera */\n        }\n    }\n';function h(e,t,o,n){var r,s,l,d,h;const{contentNode:f,clonedContentNode:g,clonedImgNodes:m,clonedVideoNodes:b,numResourcesToLoad:y,originalCanvasNodes:v}=o,{bodyClass:w,fonts:E,ignoreGlobalStyles:A,pageStyle:T,nonce:S,suppressErrors:P,copyShadowRoots:k}=n;e.onload=null;const x=null!==(r=e.contentDocument)&&void 0!==r?r:null===(s=e.contentWindow)||void 0===s?void 0:s.document;if(x){const o=x.body.appendChild(g);k&&u(f,o,!!P),E&&((null===(l=e.contentDocument)||void 0===l?void 0:l.fonts)&&(null===(d=e.contentWindow)||void 0===d?void 0:d.FontFace)?E.forEach(o=>{const n=new FontFace(o.family,o.source,{weight:o.weight,style:o.style});e.contentDocument.fonts.add(n),n.loaded.then(()=>{t(n)}).catch(e=>{t(n,["Failed loading the font:",n,"Load error:",a(e)])})}):(E.forEach(e=>{t(e)}),i({messages:['"react-to-print" is not able to load custom fonts because the browser does not support the FontFace API but will continue attempting to print the page'],suppressErrors:P})));const n=null!=T?T:p,r=x.createElement("style");S&&(r.setAttribute("nonce",S),x.head.setAttribute("nonce",S)),r.appendChild(x.createTextNode(n)),x.head.appendChild(r),w&&x.body.classList.add(...w.split(" "));const s=x.querySelectorAll("canvas");for(let e=0;e<v.length;++e){const t=v[e],o=s[e];if(void 0===o){i({messages:["A canvas element could not be copied for printing, has it loaded? `onBeforePrint` likely resolved too early.",t],suppressErrors:P});continue}const n=o.getContext("2d");n&&n.drawImage(t,0,0)}for(let e=0;e<m.length;e++){const o=m[e],n=o.getAttribute("src");if(n){const e=new Image;e.onload=()=>{t(o)},e.onerror=(e,n,r,s,i)=>{t(o,["Error loading <img>",o,"Error",i])},e.src=n}else t(o,['Found an <img> tag with an empty "src" attribute. This prevents pre-loading it.',o])}for(let e=0;e<b.length;e++){const o=b[e];o.preload="auto";const n=o.getAttribute("poster");if(n){const e=new Image;e.onload=()=>{t(o)},e.onerror=(e,r,s,i,l)=>{t(o,["Error loading video poster",n,"for video",o,"Error:",l])},e.src=n}else o.readyState>=2?t(o):o.src?(o.onloadeddata=()=>{t(o)},o.onerror=(e,n,r,s,i)=>{t(o,["Error loading video",o,"Error",i])},o.onstalled=()=>{t(o,["Loading video stalled, skipping",o])}):t(o,["Error loading video, `src` is empty",o])}const c="select",y=f.querySelectorAll(c),C=x.querySelectorAll(c);for(let e=0;e<y.length;e++)C[e].value=y[e].value;if(!A){const e=document.querySelectorAll("style, link[rel~='stylesheet'], link[as='style']");for(let o=0,n=e.length;o<n;++o){const n=e[o];if("style"===n.tagName.toLowerCase()){const e=x.createElement(n.tagName),t=n.sheet;if(t){let r="";try{const e=t.cssRules.length;for(let o=0;o<e;++o)"string"==typeof t.cssRules[o].cssText&&(r+=`${t.cssRules[o].cssText}\r\n`)}catch(e){i({messages:["A stylesheet could not be accessed. This is likely due to the stylesheet having cross-origin imports, and many browsers block script access to cross-origin stylesheets. See https://github.com/MatthewHerbst/react-to-print/issues/429 for details. You may be able to load the sheet by both marking the stylesheet with the cross `crossorigin` attribute, and setting the `Access-Control-Allow-Origin` header on the server serving the stylesheet. Alternatively, host the stylesheet on your domain to avoid this issue entirely.",n,`Original error: ${a(e).message}`],level:"warning"})}e.setAttribute("id",`react-to-print-${o}`),S&&e.setAttribute("nonce",S),e.appendChild(x.createTextNode(r)),x.head.appendChild(e)}}else if(n.getAttribute("href"))if(n.hasAttribute("disabled"))i({messages:["`react-to-print` encountered a <link> tag with a `disabled` attribute and will ignore it. Note that the `disabled` attribute is deprecated, and some browsers ignore it. You should stop using it. https://developer.mozilla.org/en-US/docs/Web/HTML/Element/link#attr-disabled. The <link> is:",n],level:"warning"}),t(n);else{const e=x.createElement(n.tagName);for(let t=0,o=n.attributes.length;t<o;++t){const o=n.attributes[t];o&&e.setAttribute(o.nodeName,null!==(h=o.nodeValue)&&void 0!==h?h:"")}e.onload=()=>{t(e)},e.onerror=(o,n,r,s,i)=>{t(e,["Failed to load",e,"Error:",i])},S&&e.setAttribute("nonce",S),x.head.appendChild(e)}else i({messages:["`react-to-print` encountered a <link> tag with an empty `href` attribute. In addition to being invalid HTML, this can cause problems in many browsers, and so the <link> was not loaded. The <link> is:",n],level:"warning"}),t(n)}}}0===y&&c(e,n)}function f({bodyClass:e,contentRef:t,copyShadowRoots:o,documentTitle:n,fonts:r,ignoreGlobalStyles:d,nonce:u,onAfterPrint:p,onBeforePrint:f,onPrintError:g,pageStyle:m,preserveAfterPrint:b,print:y,suppressErrors:v}){return(0,s.useCallback)(s=>{function w(){const l={bodyClass:e,contentRef:t,copyShadowRoots:o,documentTitle:n,fonts:r,ignoreGlobalStyles:d,nonce:u,onAfterPrint:p,onBeforePrint:f,onPrintError:g,pageStyle:m,preserveAfterPrint:b,print:y,suppressErrors:v},a=function(){const e=document.createElement("iframe");return e.width=`${document.documentElement.clientWidth}px`,e.height=`${document.documentElement.clientHeight}px`,e.style.position="absolute",e.style.top=`-${document.documentElement.clientHeight+100}px`,e.style.left=`-${document.documentElement.clientWidth+100}px`,e.id="printWindow",e.srcdoc="<!DOCTYPE html>",e}(),w=function(e,t){const{contentRef:o,fonts:n,ignoreGlobalStyles:r,suppressErrors:s}=t,l=function({contentRef:e,optionalContent:t,suppressErrors:o}){return t&&"function"==typeof t?(e&&i({level:"warning",messages:['"react-to-print" received a `contentRef` option and an optional-content param passed to its callback. The `contentRef` option will be ignored.']}),t()):e?e.current:void i({messages:['"react-to-print" did not receive a `contentRef` option or a optional-content param pass to its callback.'],suppressErrors:o})}({contentRef:o,optionalContent:e,suppressErrors:s});if(!l)return;const a=l.cloneNode(!0),c=document.querySelectorAll("link[rel~='stylesheet'], link[as='style']"),d=a.querySelectorAll("img"),u=a.querySelectorAll("video"),p=n?n.length:0;return{contentNode:l,clonedContentNode:a,clonedImgNodes:d,clonedVideoNodes:u,numResourcesToLoad:(r?0:c.length)+d.length+u.length+p,originalCanvasNodes:l.querySelectorAll("canvas")}}(s,l);if(!w)return void i({messages:["There is nothing to print"],suppressErrors:v});const E=function(e,t,o){const{suppressErrors:n}=e,r=[],s=[];return function(l,a){r.includes(l)?i({level:"debug",messages:["Tried to mark a resource that has already been handled",l],suppressErrors:n}):(a?(i({messages:['"react-to-print" was unable to load a resource but will continue attempting to print the page',...a],suppressErrors:n}),s.push(l)):r.push(l),r.length+s.length===t&&c(o,e))}}(l,w.numResourcesToLoad,a);!function(e,t,o,n){e.onload=()=>{h(e,t,o,n)},document.body.appendChild(e)}(a,E,w,l)}l(b,!0),f?f().then(()=>{w()}).catch(e=>{null==g||g("onBeforePrint",a(e))}):w()},[e,t,o,n,r,d,u,p,f,g,m,b,y,v])}return r}()});