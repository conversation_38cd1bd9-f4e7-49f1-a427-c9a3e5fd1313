# 成本分析材質克重自動查詢功能說明

## 功能概述

在成本分析頁面中，當用戶輸入材質克重時，系統會自動從紙板單價表中查詢對應的材質代號和紙板單價，並自動填入相關欄位。

## 實作內容

### 1. 後端API新增

**檔案**: `backend/routers/cardboard_pricing.py`

新增了 `search_by_weight` API端點：

```python
@router.get("/search/by-weight")
async def search_by_weight(
    material_weight: int,
    supplier_code: Optional[str] = None,
    corrugated_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """根據材質克重搜尋價格和材質代號"""
```

**功能特點**：
- 根據材質克重查詢紙板單價記錄
- 支援供應商代碼和瓦楞類型的篩選
- 只返回狀態為 "active" 的記錄
- 按日期降序排列，確保最新記錄在前

### 2. 前端API服務擴展

**檔案**: `frontend/src/services/cardboardPricingAPI.ts`

新增了 `searchByWeight` 方法：

```typescript
async searchByWeight(
  materialWeight: number, 
  supplierCode?: string, 
  corrugatedType?: string
): Promise<{ data: CardboardPricing[] }>
```

### 3. 前端頁面功能實作

**檔案**: `frontend/src/pages/CostAnalysis.tsx`

#### 新增功能：

1. **導入紙板單價API服務**
   ```typescript
   import { cardboardPricingAPI } from '../services/cardboardPricingAPI';
   ```

2. **材質克重變化處理函數**
   ```typescript
   const handlePaperQualityChange = async (value: number | null) => {
     // 自動查詢和填入邏輯
   }
   ```

3. **修改材質克重輸入欄位**
   - 添加 `onChange={handlePaperQualityChange}` 事件
   - 添加 `addonAfter="g"` 單位顯示

## 使用流程

### 用戶操作流程：

1. **進入成本分析頁面**
   - 點擊「新增成本分析」或編輯現有記錄

2. **切換到「材質資訊」標籤**
   - 在表單中找到材質相關欄位

3. **輸入材質克重**
   - 在「材質克重 / chất lượng giấy (gram)」欄位輸入數值
   - 例如：輸入 `150`

4. **系統自動處理**
   - 系統會自動查詢紙板單價表
   - 如果找到對應記錄，會自動填入：
     - 材質代號 (paper_code)
     - 紙板單價 (cardboard_unit_price)
   - 顯示成功訊息

### 系統處理邏輯：

1. **查詢條件**
   - 材質克重完全匹配
   - 狀態為 "active"
   - 按日期降序排列

2. **結果處理**
   - **找到記錄**：取最新的一筆記錄，自動填入材質代號和單價
   - **沒有記錄**：清空相關欄位，顯示警告訊息
   - **查詢失敗**：顯示錯誤訊息

3. **用戶反饋**
   - 成功：`已自動帶出材質代號：K150，單價：₫18,000`
   - 警告：`未找到材質克重 150g 的紙板單價資料`
   - 錯誤：`查詢紙板單價失敗`

## 技術細節

### API請求格式：
```
GET /api/cardboard-pricing/search/by-weight?material_weight=150
```

### 可選參數：
- `supplier_code`: 供應商代碼篩選
- `corrugated_type`: 瓦楞類型篩選

### 回應格式：
```json
[
  {
    "id": 1,
    "material_weight": 150,
    "material_code": "K150",
    "unit_price": 18000,
    "supplier_code": "SUP001",
    "corrugated_type": "C",
    "date": "2024-01-15",
    "status": "active"
  }
]
```

## 測試

**測試腳本**: `test_material_weight_lookup.py`

### 測試內容：
1. 基本材質克重查詢功能
2. 帶篩選條件的查詢
3. 創建測試資料
4. 各種邊界情況測試

### 執行測試：
```bash
python test_material_weight_lookup.py
```

## 優勢

1. **提高效率**：減少手動輸入，避免錯誤
2. **資料一致性**：確保使用最新的紙板單價資料
3. **用戶體驗**：即時反饋，操作簡便
4. **資料準確性**：直接從紙板單價表查詢，避免人為錯誤

## 注意事項

1. **資料依賴**：功能依賴紙板單價表中有對應的材質克重資料
2. **網路連接**：需要後端API正常運作
3. **權限要求**：用戶需要有成本分析的編輯權限
4. **資料更新**：如果紙板單價有更新，會自動使用最新資料

## 未來擴展

1. **多條件查詢**：可以考慮加入供應商和瓦楞類型的自動篩選
2. **歷史記錄**：顯示該材質克重的價格變化趨勢
3. **批量處理**：支援批量匯入時的自動查詢
4. **快取機制**：提高查詢效能

## 完成狀態

- ✅ 後端API實作完成
- ✅ 前端API服務完成
- ✅ 前端頁面功能完成
- ✅ 測試腳本準備完成
- ✅ 用戶體驗優化完成

功能已完全實作並可以投入使用。
