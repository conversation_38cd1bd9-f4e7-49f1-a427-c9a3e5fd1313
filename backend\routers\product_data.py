from fastapi import APIRouter, Query, Depends, HTTPException, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, date
from sqlalchemy.orm import Session
from database import get_db, Product as DBProduct
import math

router = APIRouter()

# Pydantic 模型
class ProductBase(BaseModel):
    quote_date: Optional[date] = None
    supplier_code: Optional[str] = None
    customer_name: Optional[str] = None
    product_code: str
    product_name: Optional[str] = None
    mold_number: Optional[str] = None
    design_number: Optional[str] = None
    has_waterproof: bool = False
    has_film: bool = False
    category: Optional[str] = None
    finished_size: Optional[str] = None
    material_weight: Optional[int] = None
    material_code: Optional[str] = None
    paper_width: Optional[float] = None
    cut_width: Optional[float] = None
    cut_length: Optional[float] = None
    small_sheets: Optional[int] = None
    large_sheets: Optional[int] = None
    cutting_sheets: Optional[int] = None
    corrugated: Optional[str] = None
    pressing_lines: Optional[str] = None
    meters: Optional[float] = None
    area: Optional[float] = None
    cardboard_unit_price: Optional[float] = None
    subtotal_before_tax: Optional[float] = None
    moq: Optional[int] = None
    labor_cost: Optional[float] = None
    shipping_fee: Optional[float] = None
    film_cost: Optional[float] = None
    mold_cost: Optional[float] = None
    additional_cost: Optional[float] = None
    total_cost: Optional[float] = None
    unit_price: str = "pcs"
    profit_amount: Optional[float] = None
    profit_percentage: Optional[float] = None
    description: Optional[str] = None
    is_active: bool = True
    created_by: Optional[str] = None

class ProductCreate(ProductBase):
    pass

class ProductUpdate(BaseModel):
    quote_date: Optional[date] = None
    supplier_code: Optional[str] = None
    customer_name: Optional[str] = None
    product_code: Optional[str] = None
    product_name: Optional[str] = None
    mold_number: Optional[str] = None
    design_number: Optional[str] = None
    has_waterproof: Optional[bool] = None
    has_film: Optional[bool] = None
    category: Optional[str] = None
    finished_size: Optional[str] = None
    material_weight: Optional[int] = None
    material_code: Optional[str] = None
    paper_width: Optional[float] = None
    cut_width: Optional[float] = None
    cut_length: Optional[float] = None
    small_sheets: Optional[int] = None
    large_sheets: Optional[int] = None
    cutting_sheets: Optional[int] = None
    corrugated: Optional[str] = None
    pressing_lines: Optional[str] = None
    meters: Optional[float] = None
    area: Optional[float] = None
    cardboard_unit_price: Optional[float] = None
    subtotal_before_tax: Optional[float] = None
    moq: Optional[int] = None
    labor_cost: Optional[float] = None
    shipping_fee: Optional[float] = None
    film_cost: Optional[float] = None
    mold_cost: Optional[float] = None
    additional_cost: Optional[float] = None
    total_cost: Optional[float] = None
    unit_price: Optional[str] = None
    profit_amount: Optional[float] = None
    profit_percentage: Optional[float] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None

class Product(ProductBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class PaginatedProductResponse(BaseModel):
    data: List[Product]
    total: int
    page: int
    pageSize: int
    totalPages: int

@router.get("/", response_model=PaginatedProductResponse)
async def get_products(
    page: int = Query(1, ge=1),
    pageSize: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    category: Optional[str] = None,
    supplier_code: Optional[str] = None,
    customer_name: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: Session = Depends(get_db)
):
    """取得商品列表"""
    query = db.query(DBProduct)

    # 應用篩選條件
    if search:
        query = query.filter(
            (DBProduct.product_code.contains(search)) |
            (DBProduct.product_name.contains(search)) |
            (DBProduct.mold_number.contains(search))
        )

    if category:
        query = query.filter(DBProduct.category.contains(category))

    if supplier_code:
        query = query.filter(DBProduct.supplier_code.contains(supplier_code))

    if customer_name:
        query = query.filter(DBProduct.customer_name.contains(customer_name))

    if is_active is not None:
        query = query.filter(DBProduct.is_active == is_active)

    # 計算總數
    total = query.count()

    # 分頁 - 按創建時間倒序排列
    offset = (page - 1) * pageSize
    products = query.order_by(DBProduct.created_at.desc()).offset(offset).limit(pageSize).all()

    # 計算總頁數
    total_pages = math.ceil(total / pageSize)

    return PaginatedProductResponse(
        data=products,
        total=total,
        page=page,
        pageSize=pageSize,
        totalPages=total_pages
    )

@router.get("/{product_id}", response_model=Product)
async def get_product(product_id: int, db: Session = Depends(get_db)):
    """取得單一商品資料"""
    product = db.query(DBProduct).filter(DBProduct.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="商品不存在")
    return product

@router.post("/", response_model=Product, status_code=status.HTTP_201_CREATED)
async def create_product(product: ProductCreate, db: Session = Depends(get_db)):
    """建立商品資料"""
    # 檢查產品代碼是否已存在
    existing_product = db.query(DBProduct).filter(DBProduct.product_code == product.product_code).first()
    if existing_product:
        raise HTTPException(status_code=400, detail="產品代碼已存在")

    # 創建新商品
    db_product = DBProduct(**product.dict())
    db.add(db_product)
    db.commit()
    db.refresh(db_product)
    return db_product

@router.put("/{product_id}", response_model=Product)
async def update_product(product_id: int, product_update: ProductUpdate, db: Session = Depends(get_db)):
    """更新商品資料"""
    product = db.query(DBProduct).filter(DBProduct.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="商品不存在")

    # 如果更新產品代碼，檢查是否與其他商品重複
    if product_update.product_code and product_update.product_code != product.product_code:
        existing_product = db.query(DBProduct).filter(
            DBProduct.product_code == product_update.product_code,
            DBProduct.id != product_id
        ).first()
        if existing_product:
            raise HTTPException(status_code=400, detail="產品代碼已存在")

    # 更新欄位
    update_data = product_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(product, field, value)

    db.commit()
    db.refresh(product)
    return product

@router.delete("/{product_id}")
async def delete_product(product_id: int, db: Session = Depends(get_db)):
    """刪除商品"""
    product = db.query(DBProduct).filter(DBProduct.id == product_id).first()
    if not product:
        raise HTTPException(status_code=404, detail="商品不存在")

    db.delete(product)
    db.commit()

    return {"message": "商品已刪除"}

@router.get("/categories/list")
async def get_categories_list(db: Session = Depends(get_db)):
    """取得所有分類列表"""
    categories = db.query(DBProduct.category).distinct().filter(DBProduct.category.isnot(None)).all()
    return [c.category for c in categories if c.category]

@router.get("/suppliers/list")
async def get_suppliers_list(db: Session = Depends(get_db)):
    """取得所有供應商代碼列表"""
    suppliers = db.query(DBProduct.supplier_code).distinct().filter(DBProduct.supplier_code.isnot(None)).all()
    return [s.supplier_code for s in suppliers if s.supplier_code]

@router.get("/customers/list")
async def get_customers_list(db: Session = Depends(get_db)):
    """取得所有客戶列表"""
    customers = db.query(DBProduct.customer_name).distinct().filter(DBProduct.customer_name.isnot(None)).all()
    return [c.customer_name for c in customers if c.customer_name]

@router.get("/dashboard/summary")
async def get_product_summary(db: Session = Depends(get_db)):
    """取得商品概況統計"""
    total = db.query(DBProduct).count()
    active = db.query(DBProduct).filter(DBProduct.is_active == True).count()
    inactive = db.query(DBProduct).filter(DBProduct.is_active == False).count()

    # 分類數量
    category_count = db.query(DBProduct.category).distinct().count()

    # 供應商數量
    supplier_count = db.query(DBProduct.supplier_code).distinct().count()

    # 平均利潤率
    profit_rates = db.query(DBProduct.profit_percentage).filter(DBProduct.profit_percentage.isnot(None)).all()
    avg_profit_rate = sum([p[0] for p in profit_rates if p[0]]) / len(profit_rates) if profit_rates else 0

    return {
        "total_products": total,
        "active_products": active,
        "inactive_products": inactive,
        "category_count": category_count,
        "supplier_count": supplier_count,
        "average_profit_rate": round(avg_profit_rate, 2)
    }
