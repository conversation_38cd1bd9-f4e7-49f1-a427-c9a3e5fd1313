import api from './api';

export interface CostAnalysis {
  id: number;
  quote_date?: string;
  supplier_code?: string;
  customer_name?: string;
  product_code?: string;
  product_name?: string;
  mold_number?: string;
  design_number?: string;
  has_waterproof: boolean;
  has_film: boolean;
  category?: string;
  specifications?: string;
  paper_quality?: number;
  paper_code?: string;
  paper_width?: number;
  cut_width?: number;
  cut_length?: number;
  small_sheets?: number;
  large_sheets?: number;
  cutting_sheets?: number;
  corrugated?: string;
  pressing_lines?: string;
  meters?: number;
  area?: number;
  cardboard_unit_price?: number;
  subtotal_before_tax?: number;
  moq?: number;
  labor_cost?: number;
  shipping_fee?: number;
  film_cost?: number;
  mold_cost?: number;
  additional_cost?: number;
  total_cost?: number;
  unit_price?: number;
  profit_amount?: number;
  profit_percentage?: number;
  status: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface CostAnalysisCreate {
  quote_date?: string;
  supplier_code?: string;
  customer_name?: string;
  product_code?: string;
  product_name?: string;
  mold_number?: string;
  design_number?: string;
  has_waterproof?: boolean;
  has_film?: boolean;
  category?: string;
  specifications?: string;
  paper_quality?: number;
  paper_code?: string;
  paper_width?: number;
  cut_width?: number;
  cut_length?: number;
  small_sheets?: number;
  large_sheets?: number;
  cutting_sheets?: number;
  corrugated?: string;
  pressing_lines?: string;
  meters?: number;
  area?: number;
  cardboard_unit_price?: number;
  subtotal_before_tax?: number;
  moq?: number;
  labor_cost?: number;
  shipping_fee?: number;
  film_cost?: number;
  mold_cost?: number;
  additional_cost?: number;
  total_cost?: number;
  unit_price?: number;
  profit_amount?: number;
  profit_percentage?: number;
  status?: string;
  created_by?: string;
}

export interface CostAnalysisUpdate extends CostAnalysisCreate {}

export interface PaginatedCostAnalysisResponse {
  data: CostAnalysis[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface CostAnalysisQueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
  customer_name?: string;
  category?: string;
}

export interface CustomerOption {
  id: number;
  customer_code: string;
  company_name: string;
  contact_person: string;
  display_name: string;
}

class CostAnalysisAPI {
  private baseURL = '/api/cost-analysis';

  async getCostAnalyses(params: CostAnalysisQueryParams = {}): Promise<{ data: PaginatedCostAnalysisResponse }> {
    const response = await api.get(this.baseURL, { params });
    return response;
  }

  async getCostAnalysis(id: number): Promise<{ data: CostAnalysis }> {
    const response = await api.get(`${this.baseURL}/${id}`);
    return response;
  }

  async createCostAnalysis(analysis: CostAnalysisCreate): Promise<{ data: CostAnalysis }> {
    const response = await api.post(this.baseURL, analysis);
    return response;
  }

  async updateCostAnalysis(id: number, analysis: CostAnalysisUpdate): Promise<{ data: CostAnalysis }> {
    const response = await api.put(`${this.baseURL}/${id}`, analysis);
    return response;
  }

  async deleteCostAnalysis(id: number): Promise<{ data: { message: string } }> {
    const response = await api.delete(`${this.baseURL}/${id}`);
    return response;
  }

  async getCategories(): Promise<{ data: string[] }> {
    const response = await api.get(`${this.baseURL}/categories/list`);
    return response;
  }

  async getCustomersList(): Promise<{ data: CustomerOption[] }> {
    const response = await api.get(`${this.baseURL}/customers/list`);
    return response;
  }

  async calculateCost(id: number): Promise<{ data: CostAnalysis }> {
    const response = await api.post(`${this.baseURL}/${id}/calculate`);
    return response;
  }
}

export const costAnalysisAPI = new CostAnalysisAPI();
