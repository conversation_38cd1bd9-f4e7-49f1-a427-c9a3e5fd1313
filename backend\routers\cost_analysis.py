from fastapi import APIRouter, Query, Depends, HTTPException, status
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, date
from sqlalchemy.orm import Session
from database import get_db, CostAnalysis as DBCostAnalysis
import math

router = APIRouter()

# Pydantic 模型
class CostAnalysisBase(BaseModel):
    quote_date: Optional[date] = None
    supplier_code: Optional[str] = None
    customer_name: Optional[str] = None
    product_code: Optional[str] = None
    product_name: Optional[str] = None
    mold_number: Optional[str] = None
    design_number: Optional[str] = None
    has_waterproof: bool = False
    has_film: bool = False
    category: Optional[str] = None
    specifications: Optional[str] = None
    paper_quality: Optional[int] = None
    paper_code: Optional[str] = None
    paper_width: Optional[float] = None
    cut_width: Optional[float] = None
    cut_length: Optional[float] = None
    small_sheets: Optional[int] = None
    large_sheets: Optional[int] = None
    cutting_sheets: Optional[int] = None
    corrugated: Optional[str] = None
    pressing_lines: Optional[str] = None
    meters: Optional[float] = None
    area: Optional[float] = None
    cardboard_unit_price: Optional[float] = None
    subtotal_before_tax: Optional[float] = None
    moq: Optional[int] = None
    labor_cost: Optional[float] = None
    shipping_fee: Optional[float] = None
    film_cost: Optional[float] = None
    mold_cost: Optional[float] = None
    additional_cost: Optional[float] = None
    total_cost: Optional[float] = None
    unit_price: Optional[float] = None
    profit_amount: Optional[float] = None
    profit_percentage: Optional[float] = None
    status: str = "draft"
    created_by: Optional[str] = None

class CostAnalysisCreate(CostAnalysisBase):
    pass

class CostAnalysisUpdate(BaseModel):
    quote_date: Optional[date] = None
    supplier_code: Optional[str] = None
    customer_name: Optional[str] = None
    product_code: Optional[str] = None
    product_name: Optional[str] = None
    mold_number: Optional[str] = None
    design_number: Optional[str] = None
    has_waterproof: Optional[bool] = None
    has_film: Optional[bool] = None
    category: Optional[str] = None
    specifications: Optional[str] = None
    paper_quality: Optional[int] = None
    paper_code: Optional[str] = None
    paper_width: Optional[float] = None
    cut_width: Optional[float] = None
    cut_length: Optional[float] = None
    small_sheets: Optional[int] = None
    large_sheets: Optional[int] = None
    cutting_sheets: Optional[int] = None
    corrugated: Optional[str] = None
    pressing_lines: Optional[str] = None
    meters: Optional[float] = None
    area: Optional[float] = None
    cardboard_unit_price: Optional[float] = None
    subtotal_before_tax: Optional[float] = None
    moq: Optional[int] = None
    labor_cost: Optional[float] = None
    shipping_fee: Optional[float] = None
    film_cost: Optional[float] = None
    mold_cost: Optional[float] = None
    additional_cost: Optional[float] = None
    total_cost: Optional[float] = None
    unit_price: Optional[float] = None
    profit_amount: Optional[float] = None
    profit_percentage: Optional[float] = None
    status: Optional[str] = None

class CostAnalysis(CostAnalysisBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class PaginatedCostAnalysisResponse(BaseModel):
    data: List[CostAnalysis]
    total: int
    page: int
    pageSize: int
    totalPages: int

@router.get("/", response_model=PaginatedCostAnalysisResponse)
@router.get("", response_model=PaginatedCostAnalysisResponse)
async def get_cost_analysis(
    page: int = Query(1, ge=1),
    pageSize: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    status: Optional[str] = None,
    customer_name: Optional[str] = None,
    category: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """取得成本分析列表"""
    query = db.query(DBCostAnalysis)

    # 應用篩選條件
    if search:
        query = query.filter(
            (DBCostAnalysis.product_code.contains(search)) |
            (DBCostAnalysis.product_name.contains(search)) |
            (DBCostAnalysis.customer_name.contains(search)) |
            (DBCostAnalysis.supplier_code.contains(search))
        )
    
    if status:
        query = query.filter(DBCostAnalysis.status == status)
    
    if customer_name:
        query = query.filter(DBCostAnalysis.customer_name.contains(customer_name))
        
    if category:
        query = query.filter(DBCostAnalysis.category == category)

    # 計算總數
    total = query.count()
    
    # 分頁
    offset = (page - 1) * pageSize
    cost_analyses = query.order_by(DBCostAnalysis.created_at.desc()).offset(offset).limit(pageSize).all()
    
    # 計算總頁數
    total_pages = math.ceil(total / pageSize)

    return PaginatedCostAnalysisResponse(
        data=cost_analyses,
        total=total,
        page=page,
        pageSize=pageSize,
        totalPages=total_pages
    )

@router.get("/{analysis_id}", response_model=CostAnalysis)
@router.get("/{analysis_id}/", response_model=CostAnalysis)
async def get_cost_analysis_item(analysis_id: int, db: Session = Depends(get_db)):
    """取得單一成本分析資料"""
    analysis = db.query(DBCostAnalysis).filter(DBCostAnalysis.id == analysis_id).first()
    if not analysis:
        raise HTTPException(status_code=404, detail="成本分析不存在")
    return analysis

@router.post("/", response_model=CostAnalysis, status_code=status.HTTP_201_CREATED)
@router.post("", response_model=CostAnalysis, status_code=status.HTTP_201_CREATED)
async def create_cost_analysis(analysis: CostAnalysisCreate, db: Session = Depends(get_db)):
    """建立成本分析"""
    # 創建新成本分析
    db_analysis = DBCostAnalysis(**analysis.dict())
    db.add(db_analysis)
    db.commit()
    db.refresh(db_analysis)
    return db_analysis

@router.put("/{analysis_id}", response_model=CostAnalysis)
@router.put("/{analysis_id}/", response_model=CostAnalysis)
async def update_cost_analysis(analysis_id: int, analysis_update: CostAnalysisUpdate, db: Session = Depends(get_db)):
    """更新成本分析"""
    analysis = db.query(DBCostAnalysis).filter(DBCostAnalysis.id == analysis_id).first()
    if not analysis:
        raise HTTPException(status_code=404, detail="成本分析不存在")

    # 更新欄位
    update_data = analysis_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(analysis, field, value)

    db.commit()
    db.refresh(analysis)
    return analysis

@router.delete("/{analysis_id}")
@router.delete("/{analysis_id}/")
async def delete_cost_analysis(analysis_id: int, db: Session = Depends(get_db)):
    """刪除成本分析"""
    analysis = db.query(DBCostAnalysis).filter(DBCostAnalysis.id == analysis_id).first()
    if not analysis:
        raise HTTPException(status_code=404, detail="成本分析不存在")

    db.delete(analysis)
    db.commit()
    
    return {"message": "成本分析已刪除"}

@router.get("/categories/list")
async def get_categories(db: Session = Depends(get_db)):
    """取得所有分類"""
    categories = db.query(DBCostAnalysis.category).distinct().filter(DBCostAnalysis.category.isnot(None)).all()
    return [c.category for c in categories if c.category]

@router.get("/customers/list")
async def get_customers_list(db: Session = Depends(get_db)):
    """取得所有活躍客戶列表"""
    from database import Customer as DBCustomer
    customers = db.query(DBCustomer).filter(DBCustomer.is_active == True).all()
    return [
        {
            "id": customer.id,
            "customer_code": customer.customer_code,
            "company_name": customer.company_name,
            "contact_person": customer.contact_person,
            "display_name": f"{customer.customer_code} - {customer.company_name}"
        }
        for customer in customers
    ]

@router.post("/{analysis_id}/calculate")
async def calculate_cost(analysis_id: int, db: Session = Depends(get_db)):
    """自動計算成本"""
    analysis = db.query(DBCostAnalysis).filter(DBCostAnalysis.id == analysis_id).first()
    if not analysis:
        raise HTTPException(status_code=404, detail="成本分析不存在")

    # 計算邏輯
    if analysis.area and analysis.cardboard_unit_price:
        analysis.subtotal_before_tax = analysis.area * analysis.cardboard_unit_price
    
    # 計算總成本
    total_cost = (analysis.subtotal_before_tax or 0) + \
                 (analysis.labor_cost or 0) + \
                 (analysis.shipping_fee or 0) + \
                 (analysis.film_cost or 0) + \
                 (analysis.mold_cost or 0) + \
                 (analysis.additional_cost or 0)
    
    analysis.total_cost = total_cost
    
    # 計算利潤
    if analysis.unit_price and analysis.total_cost:
        analysis.profit_amount = analysis.unit_price - analysis.total_cost
        if analysis.total_cost > 0:
            analysis.profit_percentage = (analysis.profit_amount / analysis.total_cost) * 100

    db.commit()
    db.refresh(analysis)
    
    return analysis
