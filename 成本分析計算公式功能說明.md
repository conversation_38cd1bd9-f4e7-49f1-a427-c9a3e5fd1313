# 成本分析計算公式功能說明

## 功能概述

在成本分析頁面中實作了自動計算功能，當用戶輸入相關數據時，系統會根據預設的公式自動計算相關欄位的值。

## 實作的計算公式

### 1. 基本計算公式

#### 開張數計算
```
開張數 = ROUNDDOWN(門幅/切寬, 0)
```
- **說明**：向下取整，計算一張大紙可以切出多少張
- **觸發條件**：門幅或切寬變更時
- **欄位狀態**：自動計算（只讀）

#### 大張數計算
```
大張數 = 小張數/開張數
```
- **說明**：計算需要多少張大紙
- **觸發條件**：小張數或開張數變更時
- **欄位狀態**：自動計算（只讀）

#### 米數計算
```
米數 = 切長/1000*大張數
```
- **說明**：計算總長度（單位：米）
- **觸發條件**：切長或大張數變更時
- **欄位狀態**：自動計算（只讀）

#### 平方數計算
```
平方數 = 切長/1000*門幅/1000*大張數
```
- **說明**：計算總面積（單位：平方米）
- **觸發條件**：切長、門幅或大張數變更時
- **欄位狀態**：自動計算（只讀）

### 2. 成本計算公式

#### 小計計算
```
小計 = 平方數*紙板單價
```
- **說明**：計算紙板材料成本
- **觸發條件**：平方數或紙板單價變更時
- **欄位狀態**：自動計算（只讀）

#### MOQ計算
```
MOQ = 300000/切長*開張數
```
- **說明**：計算最小訂購量
- **觸發條件**：切長或開張數變更時
- **欄位狀態**：自動計算（只讀）

#### 過膜費用計算
```
過膜 = IF(防水選項="打開", 平方數*1000, IF(防水選項="關閉", 0, ""))
```
- **說明**：根據防水選項計算過膜費用
- **觸發條件**：防水選項或平方數變更時
- **欄位狀態**：自動計算（只讀）

#### 總成本計算
```
總成本 = 小計+人工成本+運輸費+過膜+刀模費+額外開支
```
- **說明**：計算所有成本的總和
- **觸發條件**：任一成本項目變更時
- **欄位狀態**：自動計算（只讀）

### 3. 利潤計算公式

#### 利潤金額計算
```
利潤 = 單價-總成本
```
- **說明**：計算利潤金額
- **觸發條件**：單價或總成本變更時
- **欄位狀態**：自動計算（只讀）

#### 利潤百分比計算
```
利潤% = 利潤/總成本*100
```
- **說明**：計算利潤率
- **觸發條件**：利潤或總成本變更時
- **欄位狀態**：自動計算（只讀）

## 瓦楞類型更新

### 新的瓦楞選項
瓦楞欄位已更新為下拉選單，包含以下5種固定選項：
- **C**：C楞
- **B**：B楞  
- **E**：E楞
- **CB**：CB楞
- **EB**：EB楞

## 欄位狀態說明

### 輸入欄位（可編輯）
- 門幅 / KHỔ
- 切寬 / CẮT RỘNG (mm)
- 切長 / CẮT DÀI (mm)
- 小張數 / SỐ TẤM NHỎ
- 材質克重 / chất lượng giấy (gram)
- 紙板單價 / Đơn giá tấm carton
- 人工成本 / Chi phí nhân công
- 運輸費 / Phí vận chuyển
- 刀模費 / Chi phí dao khuôn
- 額外開支 / Chi phí bổ sung
- 單價 / Đơn giá
- 防水選項 / chống thấm（開關）
- 瓦楞類型（下拉選單）

### 自動計算欄位（只讀）
- 開張數 / SỐ DAO
- 大張數 / SỐ TẤM LỚN
- 米數 / SỐ MÉT (m)
- 平方數 / DIỆN TÍCH (m²)
- 小計 / 小计 (chưa tính thuế)
- MOQ
- 過膜 / qua màng
- 總成本 / Tổng chi phí
- 利潤金額 / Lợi nhuận
- 利潤百分比 / Lợi nhuận (%)

## 計算觸發機制

### 即時計算
- 使用 `setTimeout(calculateFormulas, 100)` 確保計算在輸入完成後執行
- 避免頻繁計算影響性能

### 計算順序
1. **基礎計算**：開張數 → 大張數 → 米數 → 平方數
2. **成本計算**：小計 → 過膜費用 → 總成本
3. **利潤計算**：利潤金額 → 利潤百分比

## 用戶體驗優化

### 視覺提示
- 自動計算欄位使用灰色背景 (`backgroundColor: '#f5f5f5'`)
- 標籤後綴 `(自動計算)` 提示用戶該欄位為計算結果
- 只讀屬性防止用戶誤編輯

### 數值格式化
- 貨幣欄位：`₫ 123,456` 格式
- 百分比欄位：`12.34%` 格式
- 小數保留：面積、米數保留2位小數

## 測試驗證

### 測試腳本
`test_cost_analysis_formulas.py` 提供完整的公式測試：
- 基本計算公式驗證
- 邊界情況測試
- 計算精度測試
- 瓦楞類型驗證

### 測試案例
```bash
python test_cost_analysis_formulas.py
```

## 技術實作細節

### 前端實作
- **檔案**：`frontend/src/pages/CostAnalysis.tsx`
- **計算函數**：`calculateFormulas()`
- **觸發方式**：各輸入欄位的 `onChange` 事件

### 計算邏輯
- 使用 `Math.floor()` 實現 ROUNDDOWN 功能
- 使用 `Math.round()` 進行四捨五入
- 金額計算結果取整數
- 百分比保留2位小數

## 使用流程

1. **進入成本分析頁面**
2. **填寫基本資料**：門幅、切寬、切長、小張數
3. **系統自動計算**：開張數、大張數、米數、平方數
4. **輸入材質克重**：系統自動帶出材質代號和紙板單價
5. **系統計算小計**：平方數 × 紙板單價
6. **設定防水選項**：系統自動計算過膜費用
7. **輸入其他成本**：人工、運輸、刀模、額外開支
8. **系統計算總成本**：所有成本項目總和
9. **輸入單價**：系統自動計算利潤和利潤率

## 完成狀態

- ✅ 所有計算公式實作完成
- ✅ 瓦楞類型更新為5種選項
- ✅ 自動計算欄位設為只讀
- ✅ 視覺提示和格式化完成
- ✅ 測試腳本準備完成
- ✅ 即時計算觸發機制完成

功能已完全實作並可以投入使用。
