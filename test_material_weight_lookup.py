#!/usr/bin/env python3
"""
測試材質克重查詢功能的腳本
測試根據材質克重自動帶出材質代號和紙板單價
"""

import requests
import json

def test_search_by_weight():
    """測試根據材質克重搜尋紙板單價API"""
    try:
        # 測試不同的材質克重
        test_weights = [120, 150, 200, 250, 300]
        
        for weight in test_weights:
            print(f"\n測試材質克重: {weight}g")
            print("-" * 30)
            
            response = requests.get(
                f"http://localhost:8000/api/cardboard-pricing/search/by-weight",
                params={"material_weight": weight}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"找到 {len(data)} 筆記錄")
                
                if data:
                    # 顯示前3筆記錄
                    for i, record in enumerate(data[:3]):
                        print(f"記錄 {i+1}:")
                        print(f"  材質代號: {record.get('material_code', 'N/A')}")
                        print(f"  單價: ₫{record.get('unit_price', 0):,}")
                        print(f"  供應商: {record.get('supplier_code', 'N/A')}")
                        print(f"  瓦楞類型: {record.get('corrugated_type', 'N/A')}")
                        print(f"  日期: {record.get('date', 'N/A')}")
                        print()
                else:
                    print("  沒有找到對應的記錄")
            else:
                print(f"  API請求失敗，狀態碼: {response.status_code}")
                print(f"  錯誤信息: {response.text}")
                
    except Exception as e:
        print(f"測試過程中發生錯誤: {e}")

def test_search_by_weight_with_filters():
    """測試帶篩選條件的材質克重搜尋"""
    try:
        print("\n測試帶篩選條件的搜尋")
        print("=" * 40)
        
        # 測試帶供應商篩選
        response = requests.get(
            "http://localhost:8000/api/cardboard-pricing/search/by-weight",
            params={
                "material_weight": 150,
                "supplier_code": "SUP001"  # 假設的供應商代碼
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"帶供應商篩選的結果: {len(data)} 筆記錄")
            
            if data:
                record = data[0]
                print(f"材質代號: {record.get('material_code', 'N/A')}")
                print(f"單價: ₫{record.get('unit_price', 0):,}")
                print(f"供應商: {record.get('supplier_code', 'N/A')}")
        else:
            print(f"帶篩選的搜尋失敗，狀態碼: {response.status_code}")
            
        # 測試帶瓦楞類型篩選
        response = requests.get(
            "http://localhost:8000/api/cardboard-pricing/search/by-weight",
            params={
                "material_weight": 150,
                "corrugated_type": "C"
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"\n帶瓦楞類型篩選的結果: {len(data)} 筆記錄")
            
            if data:
                record = data[0]
                print(f"材質代號: {record.get('material_code', 'N/A')}")
                print(f"單價: ₫{record.get('unit_price', 0):,}")
                print(f"瓦楞類型: {record.get('corrugated_type', 'N/A')}")
        else:
            print(f"帶瓦楞篩選的搜尋失敗，狀態碼: {response.status_code}")
            
    except Exception as e:
        print(f"篩選測試過程中發生錯誤: {e}")

def create_test_data():
    """創建一些測試用的紙板單價資料"""
    try:
        print("\n創建測試資料")
        print("=" * 20)
        
        test_data = [
            {
                "date": "2024-01-15",
                "supplier_code": "SUP001",
                "material_weight": 120,
                "material_code": "K120",
                "unit_price": 15000,
                "corrugated_type": "C",
                "currency": "VND",
                "status": "active"
            },
            {
                "date": "2024-01-15",
                "supplier_code": "SUP001",
                "material_weight": 150,
                "material_code": "K150",
                "unit_price": 18000,
                "corrugated_type": "C",
                "currency": "VND",
                "status": "active"
            },
            {
                "date": "2024-01-15",
                "supplier_code": "SUP002",
                "material_weight": 200,
                "material_code": "K200",
                "unit_price": 22000,
                "corrugated_type": "B",
                "currency": "VND",
                "status": "active"
            }
        ]
        
        for i, data in enumerate(test_data):
            response = requests.post(
                "http://localhost:8000/api/cardboard-pricing",
                json=data
            )
            
            if response.status_code == 201:
                print(f"✅ 測試資料 {i+1} 創建成功")
            else:
                print(f"❌ 測試資料 {i+1} 創建失敗: {response.status_code}")
                print(f"   錯誤: {response.text}")
                
    except Exception as e:
        print(f"創建測試資料時發生錯誤: {e}")

if __name__ == "__main__":
    print("開始測試材質克重查詢功能...")
    print("=" * 50)
    
    # 先創建一些測試資料（如果需要的話）
    create_test_data()
    
    # 測試基本搜尋功能
    test_search_by_weight()
    
    # 測試帶篩選條件的搜尋
    test_search_by_weight_with_filters()
    
    print("\n" + "=" * 50)
    print("測試完成")
    
    print("\n使用說明:")
    print("1. 在成本分析頁面輸入材質克重")
    print("2. 系統會自動查詢對應的材質代號和紙板單價")
    print("3. 如果找到多筆記錄，會使用最新的一筆")
    print("4. 如果沒有找到記錄，會顯示警告訊息")
