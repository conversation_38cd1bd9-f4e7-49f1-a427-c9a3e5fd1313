import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  message,
  Popconfirm,
  Row,
  Col,
  Typography,
  Tag,
  DatePicker,
  InputNumber,
  Switch,
  Divider,
  Tabs
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  CalculatorOutlined,
  EyeOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { costAnalysisAPI, type CostAnalysis, type CostAnalysisCreate, type CostAnalysisUpdate, type CustomerOption } from '../services/costAnalysisAPI';
import { cardboardPricingAPI } from '../services/cardboardPricingAPI';
import QuoteGenerator from '../components/QuoteGenerator';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { TextArea } = Input;


const CostAnalysisPage: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 狀態管理
  const [analyses, setAnalyses] = useState<CostAnalysis[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [viewModalVisible, setViewModalVisible] = useState<boolean>(false);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<CostAnalysis | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  const [customers, setCustomers] = useState<CustomerOption[]>([]);

  // 選取功能
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<CostAnalysis[]>([]);
  const [quoteModalVisible, setQuoteModalVisible] = useState<boolean>(false);

  // 搜尋和篩選
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  const [customerFilter, setCustomerFilter] = useState<string>('');

  // 分頁狀態
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
  });

  // 獲取成本分析列表
  const fetchAnalyses = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const params = {
        page,
        pageSize,
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter }),
        ...(categoryFilter && { category: categoryFilter }),
        ...(customerFilter && { customer_name: customerFilter }),
      };

      const response = await costAnalysisAPI.getCostAnalyses(params);
      const { data, total, page: currentPage, pageSize: currentPageSize } = response.data;

      setAnalyses(data);
      setPagination(prev => ({
        ...prev,
        current: currentPage,
        pageSize: currentPageSize,
        total,
      }));
    } catch (error) {
      console.error('Failed to fetch cost analyses:', error);
      message.error('獲取成本分析資料失敗');
    } finally {
      setLoading(false);
    }
  };

  // 獲取分類和客戶列表
  const fetchMetadata = async () => {
    try {
      const [categoriesRes, customersRes] = await Promise.all([
        costAnalysisAPI.getCategories(),
        costAnalysisAPI.getCustomersList()
      ]);
      setCategories(categoriesRes.data);
      setCustomers(customersRes.data);
    } catch (error) {
      console.error('Failed to fetch metadata:', error);
    }
  };

  // 初始化數據
  useEffect(() => {
    fetchAnalyses();
    fetchMetadata();
  }, []);

  // 搜尋處理
  const handleSearch = () => {
    fetchAnalyses(1, pagination.pageSize);
  };

  // 重置搜尋
  const handleReset = () => {
    setSearchTerm('');
    setStatusFilter('');
    setCategoryFilter('');
    setCustomerFilter('');
    setTimeout(() => {
      fetchAnalyses(1, pagination.pageSize);
    }, 100);
  };

  // 分頁變更處理
  const handleTableChange = (paginationConfig: any) => {
    fetchAnalyses(paginationConfig.current, paginationConfig.pageSize);
  };

  // 處理材質克重變化，自動查詢紙板單價
  const handlePaperQualityChange = async (value: number | null) => {
    if (!value) {
      // 如果清空克重，也清空相關欄位
      form.setFieldsValue({
        paper_code: '',
        cardboard_unit_price: null
      });
      return;
    }

    try {
      // 根據材質克重查詢紙板單價
      const response = await cardboardPricingAPI.searchByWeight(value);
      const pricingData = response.data;

      if (pricingData && pricingData.length > 0) {
        // 取最新的一筆記錄（已按日期降序排列）
        const latestPricing = pricingData[0];

        // 自動填入材質代號和紙板單價
        form.setFieldsValue({
          paper_code: latestPricing.material_code || '',
          cardboard_unit_price: latestPricing.unit_price || null
        });

        message.success(`已自動帶出材質代號：${latestPricing.material_code}，單價：₫${latestPricing.unit_price?.toLocaleString()}`);
      } else {
        // 沒有找到對應的紙板單價
        form.setFieldsValue({
          paper_code: '',
          cardboard_unit_price: null
        });
        message.warning(`未找到材質克重 ${value}g 的紙板單價資料`);
      }
    } catch (error) {
      console.error('查詢紙板單價失敗:', error);
      message.error('查詢紙板單價失敗');
    }
  };

  // 計算相關函數
  const calculateFormulas = () => {
    const values = form.getFieldsValue();
    const {
      paper_width,      // 門幅
      cut_width,        // 切寬
      cut_length,       // 切長
      small_sheets,     // 小張數
      cardboard_unit_price, // 紙板單價
      has_waterproof,   // 防水選項
      labor_cost,       // 人工成本
      shipping_fee,     // 運輸費
      film_cost,        // 過模
      mold_cost,        // 刀模費
      additional_cost,  // 額外開支
      unit_price        // 單價
    } = values;

    const updates: any = {};

    // 開張數 = ROUNDDOWN(門幅/切寬, 0)
    if (paper_width && cut_width && paper_width > 0 && cut_width > 0) {
      const cutting_sheets = Math.floor(paper_width / cut_width);
      updates.cutting_sheets = cutting_sheets;

      // 大張數 = 小張數/開張數
      if (small_sheets && cutting_sheets > 0) {
        const large_sheets = small_sheets / cutting_sheets;
        updates.large_sheets = Math.round(large_sheets * 100) / 100; // 保留2位小數

        // 米數 = 切長/1000*大張數
        if (cut_length) {
          const meters = (cut_length / 1000) * large_sheets;
          updates.meters = Math.round(meters * 100) / 100;

          // 平方數 = 切長/1000*門幅/1000*大張數
          const area = (cut_length / 1000) * (paper_width / 1000) * large_sheets;
          updates.area = Math.round(area * 100) / 100;

          // 小計 = 平方數*紙板單價
          if (cardboard_unit_price) {
            const subtotal_before_tax = area * cardboard_unit_price;
            updates.subtotal_before_tax = Math.round(subtotal_before_tax);

            // 過膜 = IF(防水選項="打開", 平方數*1000, IF(防水選項="關閉", 0, ""))
            const film_cost_calculated = has_waterproof ? area * 1000 : 0;
            updates.film_cost = Math.round(film_cost_calculated);
          }
        }

        // MOQ = 300000/切長*開張數
        if (cut_length) {
          const moq = (300000 / cut_length) * cutting_sheets;
          updates.moq = Math.round(moq);
        }
      }
    }

    // 總成本 = 小計+人工成本+運輸費+過模+刀模費+額外開支
    const costs = [
      updates.subtotal_before_tax || 0,
      labor_cost || 0,
      shipping_fee || 0,
      updates.film_cost || film_cost || 0,
      mold_cost || 0,
      additional_cost || 0
    ];
    const total_cost = costs.reduce((sum, cost) => sum + cost, 0);
    updates.total_cost = Math.round(total_cost);

    // 利潤 = 單價-總成本
    if (unit_price && total_cost > 0) {
      const profit_amount = unit_price - total_cost;
      updates.profit_amount = Math.round(profit_amount);

      // 利潤% = 利潤/總成本 * 100
      const profit_percentage = (profit_amount / total_cost) * 100;
      updates.profit_percentage = Math.round(profit_percentage * 100) / 100;
    }

    // 批量更新表單欄位
    if (Object.keys(updates).length > 0) {
      form.setFieldsValue(updates);
    }
  };

  // 顯示新增表單
  const showAddForm = () => {
    setEditMode(false);
    setCurrentAnalysis(null);
    form.resetFields();
    form.setFieldsValue({
      quote_date: dayjs(),
      has_waterproof: false,
      has_film: false,
      status: 'draft'
    });
    setModalVisible(true);
  };

  // 顯示編輯表單
  const showEditForm = (analysis: CostAnalysis) => {
    setEditMode(true);
    setCurrentAnalysis(analysis);
    form.setFieldsValue({
      ...analysis,
      quote_date: analysis.quote_date ? dayjs(analysis.quote_date) : null,
    });
    setModalVisible(true);
  };

  // 顯示查看詳情
  const showViewModal = (analysis: CostAnalysis) => {
    setCurrentAnalysis(analysis);
    setViewModalVisible(true);
  };

  // 表單提交處理
  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    try {
      const analysisData = {
        ...values,
        quote_date: values.quote_date?.format('YYYY-MM-DD'),
      };

      if (editMode && currentAnalysis) {
        // 更新成本分析
        await costAnalysisAPI.updateCostAnalysis(currentAnalysis.id, analysisData as CostAnalysisUpdate);
        message.success('成本分析更新成功');
      } else {
        // 新增成本分析
        await costAnalysisAPI.createCostAnalysis(analysisData as CostAnalysisCreate);
        message.success('成本分析新增成功');
      }
      
      setModalVisible(false);
      form.resetFields();
      fetchAnalyses(pagination.current, pagination.pageSize);
    } catch (error: any) {
      console.error('Failed to save cost analysis:', error);
      const errorMessage = error.response?.data?.detail || '操作失敗';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 刪除成本分析
  const handleDelete = async (analysis: CostAnalysis) => {
    try {
      await costAnalysisAPI.deleteCostAnalysis(analysis.id);
      message.success('成本分析已刪除');
      fetchAnalyses(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('Failed to delete cost analysis:', error);
      message.error('刪除失敗');
    }
  };

  // 計算成本
  const handleCalculate = async (analysis: CostAnalysis) => {
    try {
      await costAnalysisAPI.calculateCost(analysis.id);
      message.success('成本計算完成');
      fetchAnalyses(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('Failed to calculate cost:', error);
      message.error('計算失敗');
    }
  };

  // 表格欄位定義 - 包含所有您要求的欄位
  const columns = [
    {
      title: '報價時間 / Ngày báo giá',
      dataIndex: 'quote_date',
      key: 'quote_date',
      width: 120,
      render: (date: string) => date ? dayjs(date).format('YYYY/MM/DD') : '-',
    },
    {
      title: '供應商代碼 / Mã nhà cung cấp',
      dataIndex: 'supplier_code',
      key: 'supplier_code',
      width: 150,
    },
    {
      title: '客戶 / Khách hàng',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 120,
    },
    {
      title: '編號/料號 / Mã hàng',
      dataIndex: 'product_code',
      key: 'product_code',
      width: 150,
      fixed: 'left' as const,
    },
    {
      title: '品名 / Tên hàng',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '刀模號 / số khuôn',
      dataIndex: 'mold_number',
      key: 'mold_number',
      width: 120,
    },
    {
      title: '圖稿 / số bản in',
      dataIndex: 'design_number',
      key: 'design_number',
      width: 120,
    },
    {
      title: '防水 / chống thấm',
      dataIndex: 'has_waterproof',
      key: 'has_waterproof',
      width: 100,
      render: (hasWaterproof: boolean) => hasWaterproof ? 'O' : ' ',
    },
    {
      title: '過模 / qua màng',
      dataIndex: 'has_film',
      key: 'has_film',
      width: 100,
      render: (hasFilm: boolean) => hasFilm ? 'O' : ' ',
    },
    {
      title: '分類 / Loại',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: string) => category ? <Tag color="blue">{category}</Tag> : '-',
    },
    {
      title: '成品規格 / Quy cách (mm)',
      dataIndex: 'finished_size',
      key: 'finished_size',
      width: 150,
      ellipsis: true,
    },


    {
      title: '材質克重 / chất lượng giấy (gram)',
      dataIndex: 'paper_quality',
      key: 'paper_quality',
      width: 140,
      render: (weight: number) => weight ? `${weight}g` : '-',
    },
    {
      title: '材質代号 / mã giấy',
      dataIndex: 'paper_code',
      key: 'paper_code',
      width: 120,
    },
    {
      title: '门幅 / KHỔ',
      dataIndex: 'paper_width',
      key: 'paper_width',
      width: 100,
    },
    {
      title: '切宽 / CẮT RỘNG (mm)',
      dataIndex: 'cut_width',
      key: 'cut_width',
      width: 120,
    },
    {
      title: '切长 / CẮT DÀI (mm)',
      dataIndex: 'cut_length',
      key: 'cut_length',
      width: 120,
    },
    {
      title: '小张数 / SỐ TẤM NHỎ',
      dataIndex: 'small_sheets',
      key: 'small_sheets',
      width: 120,
    },
    {
      title: '大张数 / SỐ TẤM LỚN',
      dataIndex: 'large_sheets',
      key: 'large_sheets',
      width: 120,
    },
    {
      title: '开张数 / SỐ DAO',
      dataIndex: 'cutting_sheets',
      key: 'cutting_sheets',
      width: 120,
    },
    {
      title: '瓦楞 / SÓNG',
      dataIndex: 'corrugated',
      key: 'corrugated',
      width: 100,
    },
    {
      title: '压线 / CÁN LẰN',
      dataIndex: 'pressing_lines',
      key: 'pressing_lines',
      width: 100,
    },
    {
      title: '米数 / SỐ MÉT (m)',
      dataIndex: 'meters',
      key: 'meters',
      width: 120,
    },
    {
      title: '平方数 / DIỆN TÍCH (m²)',
      dataIndex: 'area',
      key: 'area',
      width: 140,
    },
    {
      title: '紙板單價 / Đơn Giá vnd/㎡',
      dataIndex: 'cardboard_unit_price',
      key: 'cardboard_unit_price',
      width: 150,
      render: (price: number) => price ? `₫${price.toLocaleString()}` : '-',
    },
    {
      title: '小计 / (chưa tính thuế)',
      dataIndex: 'subtotal_before_tax',
      key: 'subtotal_before_tax',
      width: 140,
      render: (amount: number) => amount ? `₫${amount.toLocaleString()}` : '-',
    },
    {
      title: 'MOQ',
      dataIndex: 'moq',
      key: 'moq',
      width: 80,
    },
    {
      title: '人工成本 / Chi phí nhân công',
      dataIndex: 'labor_cost',
      key: 'labor_cost',
      width: 150,
      render: (cost: number) => cost ? `₫${cost.toLocaleString()}` : '-',
    },
    {
      title: '运输费 / Phí vận chuyển',
      dataIndex: 'shipping_fee',
      key: 'shipping_fee',
      width: 140,
      render: (fee: number) => fee ? `₫${fee.toLocaleString()}` : '-',
    },
    {
      title: '過模費 / qua màng',
      dataIndex: 'film_cost',
      key: 'film_cost',
      width: 120,
      render: (cost: number) => cost ? `₫${cost.toLocaleString()}` : '-',
    },
    {
      title: '刀模費 / tiền khuôn',
      dataIndex: 'mold_cost',
      key: 'mold_cost',
      width: 120,
      render: (cost: number) => cost ? `₫${cost.toLocaleString()}` : '-',
    },
    {
      title: '额外开支 / Chi phí phát sinh',
      dataIndex: 'additional_cost',
      key: 'additional_cost',
      width: 150,
      render: (cost: number) => cost ? `₫${cost.toLocaleString()}` : '-',
    },
    {
      title: '總成本 / Tổng chi phí',
      dataIndex: 'total_cost',
      key: 'total_cost',
      width: 140,
      render: (cost: number) => cost ? `₫${cost.toLocaleString()}` : '-',
    },
    {
      title: '單價 / Đơn giá',
      dataIndex: 'unit_price',
      key: 'unit_price',
      width: 100,
    },
    {
      title: '利润 / Lợi nhuận',
      dataIndex: 'profit_amount',
      key: 'profit_amount',
      width: 120,
      render: (profit: number) => profit ? `₫${profit.toLocaleString()}` : '-',
    },
    {
      title: '利润率 / Lợi nhuận (%)',
      dataIndex: 'profit_percentage',
      key: 'profit_percentage',
      width: 120,
      render: (percentage: number) => percentage ? `${percentage.toFixed(2)}%` : '-',
    },
    {
      title: '狀態',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        const statusMap = {
          draft: { color: 'orange', text: '草稿' },
          approved: { color: 'green', text: '已核准' },
          rejected: { color: 'red', text: '已拒絕' }
        };
        const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status };
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      },
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right' as const,
      render: (_: any, record: CostAnalysis) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => showViewModal(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditForm(record)}
          >
            編輯
          </Button>
          <Button
            type="link"
            size="small"
            icon={<CalculatorOutlined />}
            onClick={() => handleCalculate(record)}
          >
            計算
          </Button>
          <Popconfirm
            title="確定要刪除這個成本分析嗎？"
            onConfirm={() => handleDelete(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={2}>成本分析管理</Title>
        </div>

        {/* 搜尋和篩選區域 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={5}>
            <Input
              placeholder="搜尋料號、品名或供應商代碼"
              prefix={<SearchOutlined />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="客戶"
              style={{ width: '100%' }}
              allowClear
              showSearch
              optionFilterProp="children"
              value={customerFilter || undefined}
              onChange={(value) => setCustomerFilter(value || '')}
              filterOption={(input, option) => {
                const optionText = option?.children as string;
                return optionText?.toLowerCase().includes(input.toLowerCase()) || false;
              }}
            >
              {customers.map(customer => {
                const displayText = `${customer.customer_code} - ${customer.company_name}`;
                return (
                  <Select.Option
                    key={customer.id}
                    value={customer.company_name}
                  >
                    {displayText}
                  </Select.Option>
                );
              })}
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="狀態"
              style={{ width: '100%' }}
              allowClear
              value={statusFilter || undefined}
              onChange={(value) => setStatusFilter(value || '')}
            >
              <Select.Option value="draft">草稿</Select.Option>
              <Select.Option value="approved">已核准</Select.Option>
              <Select.Option value="rejected">已拒絕</Select.Option>
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="分類"
              style={{ width: '100%' }}
              allowClear
              value={categoryFilter || undefined}
              onChange={(value) => setCategoryFilter(value || '')}
            >
              {categories.map(category => (
                <Select.Option key={category} value={category}>{category}</Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={9}>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜尋
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={showAddForm}>
                新增成本分析
              </Button>
              <Button
                type="default"
                icon={<FileTextOutlined />}
                onClick={() => setQuoteModalVisible(true)}
                disabled={selectedRows.length === 0}
              >
                生成報價單 ({selectedRows.length})
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={analyses}
          rowKey="id"
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 4000 }}
          size="small"
          rowSelection={{
            selectedRowKeys,
            onChange: (selectedRowKeys: React.Key[], selectedRows: CostAnalysis[]) => {
              setSelectedRowKeys(selectedRowKeys);
              setSelectedRows(selectedRows);
            },
          }}
        />

        {/* 新增/編輯表單 Modal */}
        <Modal
          title={editMode ? '編輯成本分析' : '新增成本分析'}
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            form.resetFields();
          }}
          footer={null}
          width={1400}
          destroyOnHidden
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleFormSubmit}
            initialValues={{
              has_waterproof: false,
              has_film: false,
              status: 'draft'
            }}
          >
            <Tabs
              defaultActiveKey="1"
              items={[
                {
                  key: "1",
                  label: "基本資訊",
                  children: (
                    <>
                      <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item
                      name="quote_date"
                      label="報價時間 / Ngày báo giá"
                    >
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="supplier_code"
                      label="供應商代碼 / Mã nhà cung cấp"
                    >
                      <Input placeholder="輸入供應商代碼" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="customer_name"
                      label="客戶 / Khách hàng"
                    >
                      <Select
                        placeholder="選擇客戶"
                        showSearch
                        allowClear
                        optionFilterProp="children"
                        filterOption={(input, option) => {
                          const optionText = option?.children as string;
                          return optionText?.toLowerCase().includes(input.toLowerCase()) || false;
                        }}
                      >
                        {customers.map(customer => {
                          const displayText = `${customer.customer_code} - ${customer.company_name}${customer.contact_person ? ` (${customer.contact_person})` : ''}`;
                          return (
                            <Select.Option
                              key={customer.id}
                              value={customer.company_name}
                            >
                              {displayText}
                            </Select.Option>
                          );
                        })}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="product_code"
                      label="料號 / Mã hàng"
                    >
                      <Input placeholder="輸入料號" />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      name="product_name"
                      label="品名 / Tên hàng"
                    >
                      <Input placeholder="輸入品名" />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      name="mold_number"
                      label="刀模號 / số khuôn"
                    >
                      <Input placeholder="刀模號" />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      name="design_number"
                      label="圖稿 / số bản in"
                    >
                      <Input placeholder="圖稿" />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      name="has_waterproof"
                      label="防水 / chống thấm"
                      valuePropName="checked"
                    >
                      <Switch
                        checkedChildren="O"
                        unCheckedChildren=" "
                        onChange={() => setTimeout(calculateFormulas, 100)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      name="has_film"
                      label="過模 / qua màng"
                      valuePropName="checked"
                    >
                      <Switch checkedChildren="O" unCheckedChildren=" " />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item
                      name="category"
                      label="分類 / Loại"
                    >
                      <Select
                        placeholder="選擇或輸入分類"
                        showSearch
                        allowClear
                        mode="combobox"
                      >
                        {categories.map(category => (
                          <Select.Option key={category} value={category}>{category}</Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="specifications"
                      label="成品規格 / Quy cách (mm)"
                    >
                      <Input placeholder="規格" />
                    </Form.Item>
                  </Col>

                      </Row>
                    </>
                  )
                },
                {
                  key: "2",
                  label: "材質資訊",
                  children: (
                    <>
                      <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item
                      name="paper_quality"
                      label="材質克重 / chất lượng giấy (gram)"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="克重"
                        min={0}
                        addonAfter="g"
                        onChange={handlePaperQualityChange}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="paper_code"
                      label="材質代号 / mã giấy"
                    >
                      <Input placeholder="材質代号" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="paper_width"
                      label="门幅 / KHỔ"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="门幅"
                        min={0}
                        onChange={() => setTimeout(calculateFormulas, 100)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="cut_width"
                      label="切宽 / CẮT RỘNG (mm)"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="切宽"
                        min={0}
                        onChange={() => setTimeout(calculateFormulas, 100)}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item
                      name="cut_length"
                      label="切长 / CẮT DÀI (mm)"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="切长"
                        min={0}
                        onChange={() => setTimeout(calculateFormulas, 100)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="small_sheets"
                      label="小张数 / SỐ TẤM NHỎ"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="小张数"
                        min={0}
                        onChange={() => setTimeout(calculateFormulas, 100)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="large_sheets"
                      label="大张数 / SỐ TẤM LỚN (自動計算)"
                    >
                      <InputNumber
                        style={{ width: '100%', backgroundColor: '#f5f5f5' }}
                        placeholder="自動計算"
                        min={0}
                        readOnly
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="cutting_sheets"
                      label="开张数 / SỐ DAO (自動計算)"
                    >
                      <InputNumber
                        style={{ width: '100%', backgroundColor: '#f5f5f5' }}
                        placeholder="自動計算"
                        min={0}
                        readOnly
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item
                      name="corrugated"
                      label="瓦楞 / SÓNG"
                    >
                      <Select placeholder="選擇瓦楞類型" allowClear>
                        <Select.Option value="C">C</Select.Option>
                        <Select.Option value="B">B</Select.Option>
                        <Select.Option value="E">E</Select.Option>
                        <Select.Option value="CB">CB</Select.Option>
                        <Select.Option value="EB">EB</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="pressing_lines"
                      label="压线 / CÁN LẰN"
                    >
                      <Input placeholder="压线" />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="meters"
                      label="米数 / SỐ MÉT (m) (自動計算)"
                    >
                      <InputNumber
                        style={{ width: '100%', backgroundColor: '#f5f5f5' }}
                        placeholder="自動計算"
                        min={0}
                        step={0.01}
                        readOnly
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      name="area"
                      label="平方数 / DIỆN TÍCH (m²) (自動計算)"
                    >
                      <InputNumber
                        style={{ width: '100%', backgroundColor: '#f5f5f5' }}
                        placeholder="自動計算"
                        min={0}
                        step={0.01}
                        readOnly
                      />
                    </Form.Item>
                  </Col>
                      </Row>
                    </>
                  )
                },
                {
                  key: "3",
                  label: "成本計算",
                  children: (
                    <>
                      <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      name="cardboard_unit_price"
                      label="紙板單價 / Đơn Giá (vnd/㎡)"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="紙板單價"
                        min={0}
                        formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                        onChange={() => setTimeout(calculateFormulas, 100)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="subtotal_before_tax"
                      label="小计 / 小计 (chưa tính thuế) (自動計算)"
                    >
                      <InputNumber
                        style={{ width: '100%', backgroundColor: '#f5f5f5' }}
                        placeholder="自動計算"
                        min={0}
                        formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                        readOnly
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="moq"
                      label="MOQ (自動計算)"
                    >
                      <InputNumber
                        style={{ width: '100%', backgroundColor: '#f5f5f5' }}
                        placeholder="自動計算"
                        min={0}
                        readOnly
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      name="labor_cost"
                      label="人工成本 / Chi phí nhân công"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="人工成本"
                        min={0}
                        formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                        onChange={() => setTimeout(calculateFormulas, 100)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="shipping_fee"
                      label="运输费 / Phí vận chuyển"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="运输费"
                        min={0}
                        formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                        onChange={() => setTimeout(calculateFormulas, 100)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="film_cost"
                      label="過模 / qua màng (自動計算)"
                    >
                      <InputNumber
                        style={{ width: '100%', backgroundColor: '#f5f5f5' }}
                        placeholder="自動計算"
                        min={0}
                        formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                        readOnly
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      name="mold_cost"
                      label="刀模費 / tiền khuôn"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="刀模費"
                        min={0}
                        formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                        onChange={() => setTimeout(calculateFormulas, 100)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="additional_cost"
                      label="额外开支 / Chi phí phát sinh"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="额外开支"
                        min={0}
                        formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                        onChange={() => setTimeout(calculateFormulas, 100)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="total_cost"
                      label="總成本 / Tổng chi phí (自動計算)"
                    >
                      <InputNumber
                        style={{ width: '100%', backgroundColor: '#f5f5f5' }}
                        placeholder="自動計算"
                        min={0}
                        formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                        readOnly
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      name="unit_price"
                      label="單價 / Đơn giá"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="單價"
                        min={0}
                        formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                        onChange={() => setTimeout(calculateFormulas, 100)}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="profit_amount"
                      label="利润金額 / Lợi nhuận (自動計算)"
                    >
                      <InputNumber
                        style={{ width: '100%', backgroundColor: '#f5f5f5' }}
                        placeholder="自動計算"
                        formatter={value => `₫ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                        parser={value => value!.replace(/₫\s?|(,*)/g, '')}
                        readOnly
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="profit_percentage"
                      label="利润百分比 / Lợi nhuận (%) (自動計算)"
                    >
                      <InputNumber
                        style={{ width: '100%', backgroundColor: '#f5f5f5' }}
                        placeholder="自動計算"
                        min={0}
                        max={100}
                        formatter={value => `${value}%`}
                        parser={value => value!.replace('%', '')}
                        readOnly
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="status"
                      label="狀態"
                    >
                      <Select>
                        <Select.Option value="draft">草稿</Select.Option>
                        <Select.Option value="approved">已核准</Select.Option>
                        <Select.Option value="rejected">已拒絕</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="created_by"
                      label="建立者"
                    >
                      <Input placeholder="建立者" />
                    </Form.Item>
                  </Col>
                      </Row>
                    </>
                  )
                }
              ]}
            />

            <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
              <Space>
                <Button onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                }}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit" loading={loading}>
                  {editMode ? '更新' : '新增'}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        {/* 查看詳情 Modal */}
        <Modal
          title="成本分析詳情"
          open={viewModalVisible}
          onCancel={() => setViewModalVisible(false)}
          footer={[
            <Button key="close" onClick={() => setViewModalVisible(false)}>
              關閉
            </Button>
          ]}
          width={1200}
        >
          {currentAnalysis && (
            <div>
              <Tabs
                defaultActiveKey="1"
                items={[
                  {
                    key: "1",
                    label: "基本資訊",
                    children: (
                      <>
                        <Row gutter={16}>
                    <Col span={6}>
                      <Text strong>報價時間：</Text>
                      <Text>{currentAnalysis.quote_date ? dayjs(currentAnalysis.quote_date).format('YYYY/MM/DD') : '-'}</Text>
                    </Col>
                    <Col span={6}>
                      <Text strong>供應商代碼：</Text>
                      <Text>{currentAnalysis.supplier_code || '-'}</Text>
                    </Col>
                    <Col span={6}>
                      <Text strong>客戶：</Text>
                      <Text>{currentAnalysis.customer_name || '-'}</Text>
                    </Col>
                    <Col span={6}>
                      <Text strong>料號：</Text>
                      <Text>{currentAnalysis.product_code || '-'}</Text>
                    </Col>
                  </Row>
                  <br />
                  <Row gutter={16}>
                    <Col span={8}>
                      <Text strong>品名：</Text>
                      <Text>{currentAnalysis.product_name || '-'}</Text>
                    </Col>
                    <Col span={4}>
                      <Text strong>刀模號：</Text>
                      <Text>{currentAnalysis.mold_number || '-'}</Text>
                    </Col>
                    <Col span={4}>
                      <Text strong>圖稿：</Text>
                      <Text>{currentAnalysis.design_number || '-'}</Text>
                    </Col>
                    <Col span={4}>
                      <Text strong>防水：</Text>
                      <Text>{currentAnalysis.has_waterproof ? 'O' : ' '}</Text>
                    </Col>
                    <Col span={4}>
                      <Text strong>過模：</Text>
                      <Text>{currentAnalysis.has_film ? 'O' : ' '}</Text>
                    </Col>
                        </Row>
                      </>
                    )
                  },
                  {
                    key: "2",
                    label: "成本計算",
                    children: (
                      <>
                        <Row gutter={16}>
                    <Col span={8}>
                      <Text strong>紙板單價：</Text>
                      <Text>{currentAnalysis.cardboard_unit_price ? `₫${currentAnalysis.cardboard_unit_price.toLocaleString()}` : '-'}</Text>
                    </Col>
                    <Col span={8}>
                      <Text strong>小計：</Text>
                      <Text>{currentAnalysis.subtotal_before_tax ? `₫${currentAnalysis.subtotal_before_tax.toLocaleString()}` : '-'}</Text>
                    </Col>
                    <Col span={8}>
                      <Text strong>總成本：</Text>
                      <Text>{currentAnalysis.total_cost ? `₫${currentAnalysis.total_cost.toLocaleString()}` : '-'}</Text>
                    </Col>
                  </Row>
                  <br />
                  <Row gutter={16}>
                    <Col span={8}>
                      <Text strong>單價：</Text>
                      <Text>{currentAnalysis.unit_price ? `₫${currentAnalysis.unit_price.toLocaleString()}` : '-'}</Text>
                    </Col>
                    <Col span={8}>
                      <Text strong>利潤金額：</Text>
                      <Text>{currentAnalysis.profit_amount ? `₫${currentAnalysis.profit_amount.toLocaleString()}` : '-'}</Text>
                    </Col>
                    <Col span={8}>
                      <Text strong>利潤百分比：</Text>
                      <Text>{currentAnalysis.profit_percentage ? `${currentAnalysis.profit_percentage.toFixed(1)}%` : '-'}</Text>
                    </Col>
                        </Row>
                      </>
                    )
                  }
                ]}
              />
            </div>
          )}
        </Modal>

        {/* 報價單生成器 */}
        <QuoteGenerator
          visible={quoteModalVisible}
          onCancel={() => setQuoteModalVisible(false)}
          selectedAnalyses={selectedRows}
        />
      </Card>
    </div>
  );
};

export default CostAnalysisPage;
