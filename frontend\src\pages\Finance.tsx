import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  DatePicker,
  Select,
  Table,
  Tag,
  Progress,
  Tabs,
  Button,
  Space,
  message
} from 'antd';
import {
  DollarOutlined,
  RiseOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  ExportOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import type { ColumnsType } from 'antd/es/table';
import { financeAPI } from '../services/api';
import dayjs from 'dayjs';

const { Title } = Typography;
const { RangePicker } = DatePicker;


interface FinancialData {
  period: string;
  revenue: number;
  cost: number;
  gross_profit: number;
  expenses: number;
  net_profit: number;
  profit_margin: number;
}

interface AccountReceivable {
  customer_id: number;
  customer_name: string;
  invoice_no: string;
  invoice_date: string;
  due_date: string;
  amount: number;
  paid_amount: number;
  outstanding_amount: number;
  overdue_days: number;
}

interface AccountPayable {
  supplier_id: number;
  supplier_name: string;
  invoice_no: string;
  invoice_date: string;
  due_date: string;
  amount: number;
  paid_amount: number;
  outstanding_amount: number;
  overdue_days: number;
}

const Finance: React.FC = () => {
  const { t } = useTranslation();

  // Handle export report
  const handleExportReport = () => {
    message.success(t('finance.exportInDevelopment'));
    // TODO: Implement actual export functionality
  };

  // Handle print report
  const handlePrintReport = () => {
    window.print();
    message.success(t('finance.preparingPrint'));
  };

  // Financial data state
  const [financialData, setFinancialData] = useState<FinancialData>({
    period: '',
    revenue: 0,
    cost: 0,
    gross_profit: 0,
    expenses: 0,
    net_profit: 0,
    profit_margin: 0
  });
  const [loading, setLoading] = useState(false);

  // Accounts receivable state
  const [receivables, setReceivables] = useState<AccountReceivable[]>([]);

  // Accounts payable state
  const [payables, setPayables] = useState<AccountPayable[]>([]);

  // Cash flow state
  const [cashFlowData, setCashFlowData] = useState({
    cashInflow: 0,
    cashOutflow: 0,
    netCashFlow: 0
  });

  // Date range state
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(1, 'month'),
    dayjs()
  ]);
  const [reportPeriod, setReportPeriod] = useState('monthly');

  // Fetch financial report data
  const fetchFinancialReport = async () => {
    if (!dateRange || dateRange.length !== 2) return;

    setLoading(true);
    try {
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      const response = await financeAPI.getFinancialReport(reportPeriod, startDate, endDate);
      setFinancialData(response.data);
      message.success(t('finance.reportDataUpdated'));
    } catch (error) {
      console.error('Failed to fetch financial report:', error);
      message.error(t('finance.fetchReportFailed'));
    } finally {
      setLoading(false);
    }
  };

  // Fetch cash flow data
  const fetchCashFlowData = async () => {
    if (!dateRange || dateRange.length !== 2) return;

    try {
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      const response = await financeAPI.getCashFlowReport(startDate, endDate);
      setCashFlowData({
        cashInflow: response.data.cash_inflow,
        cashOutflow: response.data.cash_outflow,
        netCashFlow: response.data.net_cash_flow
      });
    } catch (error) {
      console.error('Failed to fetch cash flow data:', error);
      message.error(t('finance.fetchCashFlowFailed'));
    }
  };

  // Fetch accounts receivable data
  const fetchReceivables = async () => {
    try {
      const response = await financeAPI.getAccountsReceivable();
      setReceivables(response.data);
    } catch (error) {
      console.error('Failed to fetch accounts receivable:', error);
      message.error(t('finance.fetchReceivablesFailed'));
    }
  };

  // Fetch accounts payable data
  const fetchPayables = async () => {
    try {
      const response = await financeAPI.getAccountsPayable();
      setPayables(response.data);
    } catch (error) {
      console.error('Failed to fetch accounts payable:', error);
      message.error(t('finance.fetchPayablesFailed'));
    }
  };

  // Fetch all data
  const fetchAllData = async () => {
    await Promise.all([
      fetchFinancialReport(),
      fetchCashFlowData(),
      fetchReceivables(),
      fetchPayables()
    ]);
  };

  // Handle date range change
  const handleDateRangeChange = (dates: any) => {
    setDateRange(dates);
  };

  // Handle report period change
  const handlePeriodChange = (value: string) => {
    setReportPeriod(value);
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchAllData();
  }, [dateRange, reportPeriod]);

  const getOverdueStatus = (overdueDays: number) => {
    if (overdueDays > 30) return { color: 'red', text: t('finance.severelyOverdue') };
    if (overdueDays > 0) return { color: 'orange', text: t('finance.overdue') };
    return { color: 'green', text: t('finance.normal') };
  };

  const receivableColumns: ColumnsType<AccountReceivable> = [
    {
      title: t('finance.customerName'),
      dataIndex: 'customer_name',
      key: 'customer_name',
    },
    {
      title: t('finance.invoiceNumber'),
      dataIndex: 'invoice_no',
      key: 'invoice_no',
      width: 140,
    },
    {
      title: t('finance.invoiceDate'),
      dataIndex: 'invoice_date',
      key: 'invoice_date',
      width: 120,
    },
    {
      title: t('finance.dueDate'),
      dataIndex: 'due_date',
      key: 'due_date',
      width: 120,
    },
    {
      title: t('finance.invoiceAmount'),
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount: number) => `${amount.toLocaleString()} VND`,
    },
    {
      title: t('finance.paidAmount'),
      dataIndex: 'paid_amount',
      key: 'paid_amount',
      width: 120,
      render: (amount: number) => `${amount.toLocaleString()} VND`,
    },
    {
      title: t('finance.outstandingAmount'),
      dataIndex: 'outstanding_amount',
      key: 'outstanding_amount',
      width: 120,
      render: (amount: number) => (
        <span style={{ color: amount > 0 ? '#ff4d4f' : '#52c41a' }}>
          {amount.toLocaleString()} VND
        </span>
      ),
    },
    {
      title: t('finance.status'),
      dataIndex: 'overdue_days',
      key: 'status',
      width: 100,
      render: (days: number) => {
        const status = getOverdueStatus(days);
        return <Tag color={status.color}>{status.text}</Tag>;
      },
    },
  ];

  const payableColumns: ColumnsType<AccountPayable> = [
    {
      title: t('finance.supplierName'),
      dataIndex: 'supplier_name',
      key: 'supplier_name',
    },
    {
      title: t('finance.invoiceNumber'),
      dataIndex: 'invoice_no',
      key: 'invoice_no',
      width: 140,
    },
    {
      title: t('finance.invoiceDate'),
      dataIndex: 'invoice_date',
      key: 'invoice_date',
      width: 120,
    },
    {
      title: t('finance.dueDate'),
      dataIndex: 'due_date',
      key: 'due_date',
      width: 120,
    },
    {
      title: t('finance.invoiceAmount'),
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (amount: number) => `${amount.toLocaleString()} VND`,
    },
    {
      title: t('finance.paidAmount'),
      dataIndex: 'paid_amount',
      key: 'paid_amount',
      width: 120,
      render: (amount: number) => `${amount.toLocaleString()} VND`,
    },
    {
      title: t('finance.unpaidAmount'),
      dataIndex: 'outstanding_amount',
      key: 'outstanding_amount',
      width: 120,
      render: (amount: number) => (
        <span style={{ color: amount > 0 ? '#ff4d4f' : '#52c41a' }}>
          {amount.toLocaleString()} VND
        </span>
      ),
    },
    {
      title: t('finance.status'),
      dataIndex: 'overdueDays',
      key: 'status',
      width: 100,
      render: (days: number) => {
        const status = getOverdueStatus(days);
        return <Tag color={status.color}>{status.text}</Tag>;
      },
    },
  ];

  return (
    <div>
      <Title level={2}>{t('finance.title')}</Title>
      
      {/* Control panel */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <RangePicker
            style={{ width: '100%' }}
            value={dateRange as any}
            onChange={handleDateRangeChange}
            placeholder={[t('finance.startDate'), t('finance.endDate')]}
          />
        </Col>
        <Col span={6}>
          <Select
            placeholder={t('finance.selectReportPeriod')}
            style={{ width: '100%' }}
            value={reportPeriod}
            onChange={handlePeriodChange}
          >
            <Select.Option value="daily">{t('finance.dailyReport')}</Select.Option>
            <Select.Option value="weekly">{t('finance.weeklyReport')}</Select.Option>
            <Select.Option value="monthly">{t('finance.monthlyReport')}</Select.Option>
            <Select.Option value="yearly">{t('finance.yearlyReport')}</Select.Option>
          </Select>
        </Col>
        <Col span={10}>
          <Space>
            <Button
              type="primary"
              icon={<ExportOutlined />}
              onClick={handleExportReport}
            >
              {t('finance.exportReport')}
            </Button>
            <Button
              icon={<FileTextOutlined />}
              onClick={handlePrintReport}
            >
              {t('finance.printReport')}
            </Button>
            <Button
              loading={loading}
              onClick={fetchAllData}
            >
              {t('common.refreshData')}
            </Button>
          </Space>
        </Col>
      </Row>

      {/* Financial overview */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={4}>
          <Card loading={loading}>
            <Statistic
              title={t('finance.revenue')}
              value={financialData.revenue}
              prefix={<ArrowUpOutlined style={{ color: '#3f8600' }} />}
              suffix="VND"
              formatter={(value) => `${Number(value).toLocaleString()}`}
              valueStyle={{ color: '#3f8600' }}
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card loading={loading}>
            <Statistic
              title={t('finance.cost')}
              value={financialData.cost}
              prefix={<ArrowDownOutlined style={{ color: '#cf1322' }} />}
              suffix="VND"
              formatter={(value) => `${Number(value).toLocaleString()}`}
              valueStyle={{ color: '#cf1322' }}
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card loading={loading}>
            <Statistic
              title={t('finance.grossProfit')}
              value={financialData.gross_profit}
              prefix={<DollarOutlined />}
              suffix="VND"
              formatter={(value) => `${Number(value).toLocaleString()}`}
              valueStyle={{ color: '#1890ff' }}
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card loading={loading}>
            <Statistic
              title={t('finance.expenses')}
              value={financialData.expenses}
              suffix="VND"
              formatter={(value) => `${Number(value).toLocaleString()}`}
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card loading={loading}>
            <Statistic
              title={t('finance.netProfit')}
              value={financialData.net_profit}
              suffix="VND"
              formatter={(value) => `${Number(value).toLocaleString()}`}
              valueStyle={{ color: '#722ed1' }}
              precision={0}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card loading={loading}>
            <Statistic
              title={t('finance.profitMargin')}
              value={financialData.profit_margin}
              suffix="%"
              valueStyle={{ color: '#eb2f96' }}
              precision={2}
            />
          </Card>
        </Col>
      </Row>

      {/* Profit analysis */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={12}>
          <Card title={t('finance.revenueAndCostAnalysis')}>
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <span>{t('finance.revenue')}</span>
                <span>{financialData.revenue.toLocaleString()} VND</span>
              </div>
              <Progress
                percent={100}
                strokeColor="#52c41a"
                showInfo={false}
              />
            </div>
            <div style={{ marginBottom: 16 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <span>{t('finance.cost')}</span>
                <span>{financialData.cost.toLocaleString()} VND</span>
              </div>
              <Progress
                percent={(financialData.cost / financialData.revenue) * 100}
                strokeColor="#ff4d4f" 
                showInfo={false}
              />
            </div>
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <span>{t('finance.grossProfit')}</span>
                <span>{financialData.gross_profit.toLocaleString()} VND</span>
              </div>
              <Progress
                percent={(financialData.gross_profit / financialData.revenue) * 100}
                strokeColor="#1890ff"
                showInfo={false}
              />
            </div>
          </Card>
        </Col>
        <Col span={12}>
          <Card title={t('finance.cashFlowOverview')} loading={loading}>
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title={t('finance.cashInflow')}
                  value={cashFlowData.cashInflow}
                  suffix="VND"
                  formatter={(value) => `${Number(value).toLocaleString()}`}
                  valueStyle={{ color: '#3f8600' }}
                  precision={0}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title={t('finance.cashOutflow')}
                  value={cashFlowData.cashOutflow}
                  suffix="VND"
                  formatter={(value) => `${Number(value).toLocaleString()}`}
                  valueStyle={{ color: '#cf1322' }}
                  precision={0}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title={t('finance.netCashFlow')}
                  value={cashFlowData.netCashFlow}
                  suffix="VND"
                  formatter={(value) => `${Number(value).toLocaleString()}`}
                  valueStyle={{
                    color: cashFlowData.netCashFlow >= 0 ? '#3f8600' : '#cf1322'
                  }}
                  precision={0}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Accounts receivable and payable */}
      <Card>
        <Tabs
          defaultActiveKey="receivables"
          items={[
            {
              key: "receivables",
              label: `${t('finance.accountsReceivable')} (${receivables.length})`,
              children: (
                <Table
                  columns={receivableColumns}
                  dataSource={receivables}
                  rowKey="id"
                  loading={loading}
                  pagination={{
                    total: receivables.length,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `${t('common.page')} ${range[0]}-${range[1]} ${t('common.items')}, ${t('common.total')} ${total} ${t('common.items')}`,
                  }}
                />
              )
            },
            {
              key: "payables",
              label: `${t('finance.accountsPayable')} (${payables.length})`,
              children: (
                <Table
                  columns={payableColumns}
                  dataSource={payables}
                  rowKey="id"
                  loading={loading}
                  pagination={{
                    total: payables.length,
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) =>
                      `${t('common.page')} ${range[0]}-${range[1]} ${t('common.items')}, ${t('common.total')} ${total} ${t('common.items')}`,
                  }}
                />
              )
            }
          ]}
        />
      </Card>
    </div>
  );
};

export default Finance;
