#!/usr/bin/env python3
"""
測試輔料單價修改的腳本
測試供應商代碼、商品分類、單位選項是否正確
"""

import requests
import json

def test_suppliers_list():
    """測試供應商列表API"""
    try:
        response = requests.get("http://localhost:8000/api/auxiliary-pricing/suppliers/list")
        if response.status_code == 200:
            data = response.json()
            print("供應商列表API測試結果:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            if data:
                print(f"\n✅ 成功獲取 {len(data)} 個供應商")
                print("供應商格式檢查:")
                for supplier in data[:3]:  # 檢查前3個
                    required_fields = ["supplier_code", "company_name", "supplier_type", "display_name"]
                    missing_fields = [field for field in required_fields if field not in supplier]
                    if missing_fields:
                        print(f"❌ 供應商 {supplier.get('supplier_code', 'Unknown')} 缺少欄位: {missing_fields}")
                    else:
                        print(f"✅ 供應商 {supplier['supplier_code']} 格式正確")
            else:
                print("⚠️ 沒有獲取到供應商資料，可能是資料庫中沒有供應商記錄")
        else:
            print(f"❌ 供應商列表API請求失敗，狀態碼: {response.status_code}")
            print(f"錯誤信息: {response.text}")
    except Exception as e:
        print(f"❌ 供應商列表測試過程中發生錯誤: {e}")

def test_categories_list():
    """測試商品分類列表API"""
    try:
        response = requests.get("http://localhost:8000/api/auxiliary-pricing/categories/list")
        if response.status_code == 200:
            data = response.json()
            print("\n商品分類列表API測試結果:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            expected_categories = [
                "膠帶類", "包裝材料", "印刷耗材", "機械配件", 
                "辦公用品", "清潔用品", "安全防護", "其他輔料"
            ]
            
            print(f"\n期望的分類數量: {len(expected_categories)}")
            print(f"實際返回的分類數量: {len(data)}")
            
            missing_categories = [cat for cat in expected_categories if cat not in data]
            if missing_categories:
                print(f"❌ 缺少的預設分類: {missing_categories}")
            else:
                print("✅ 所有預設分類都存在")
                
        else:
            print(f"❌ 商品分類列表API請求失敗，狀態碼: {response.status_code}")
    except Exception as e:
        print(f"❌ 商品分類列表測試過程中發生錯誤: {e}")

def test_units_list():
    """測試單位列表API"""
    try:
        response = requests.get("http://localhost:8000/api/auxiliary-pricing/units/list")
        if response.status_code == 200:
            data = response.json()
            print("\n單位列表API測試結果:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            
            expected_units = ["PCS", "KG", "M", "M²", "SET", "BOX", "L", "cuộn(卷)"]
            
            print(f"\n期望的單位: {expected_units}")
            print(f"實際返回的單位: {data}")
            
            missing_units = [unit for unit in expected_units if unit not in data]
            if missing_units:
                print(f"❌ 缺少的單位: {missing_units}")
            else:
                print("✅ 所有預設單位都存在")
                
            # 檢查新增的單位
            new_units = ["L", "cuộn(卷)"]
            found_new_units = [unit for unit in new_units if unit in data]
            print(f"新增的單位檢查: {found_new_units}")
            if len(found_new_units) == len(new_units):
                print("✅ 新增的單位都存在")
            else:
                print(f"❌ 部分新增單位缺失: {set(new_units) - set(found_new_units)}")
                
        else:
            print(f"❌ 單位列表API請求失敗，狀態碼: {response.status_code}")
    except Exception as e:
        print(f"❌ 單位列表測試過程中發生錯誤: {e}")

def test_pricing_summary():
    """測試輔料單價概況統計API"""
    try:
        response = requests.get("http://localhost:8000/api/auxiliary-pricing/dashboard/summary")
        if response.status_code == 200:
            data = response.json()
            print("\n輔料單價概況統計API測試結果:")
            print(json.dumps(data, indent=2, ensure_ascii=False))
            print("✅ 概況統計API正常")
        else:
            print(f"❌ 概況統計API請求失敗，狀態碼: {response.status_code}")
    except Exception as e:
        print(f"❌ 概況統計測試過程中發生錯誤: {e}")

if __name__ == "__main__":
    print("開始測試輔料單價修改...")
    print("=" * 50)
    
    test_suppliers_list()
    test_categories_list()
    test_units_list()
    test_pricing_summary()
    
    print("\n" + "=" * 50)
    print("測試完成")
