#!/usr/bin/env python3
"""
資料庫遷移腳本：刪除長/D、寬/R、高/C三個尺寸欄位
"""

from sqlalchemy import create_engine, text
import os
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

# 資料庫連接設定
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./erp_system.db")

def remove_dimension_fields():
    """刪除長/D、寬/R、高/C三個尺寸欄位"""
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as connection:
            # 開始事務
            trans = connection.begin()
            
            try:
                print("開始刪除尺寸欄位...")
                
                # 對於 SQLite，需要重新創建表格來刪除欄位
                if "sqlite" in DATABASE_URL.lower():
                    print("檢測到 SQLite 資料庫，使用表格重建方式...")
                    
                    # 1. 重建 products 表格
                    print("重建 products 表格...")
                    connection.execute(text("""
                        CREATE TABLE products_new (
                            id INTEGER PRIMARY KEY,
                            quote_date DATE,
                            supplier_code VARCHAR,
                            customer_name VARCHAR,
                            product_code VARCHAR UNIQUE,
                            product_name VARCHAR,
                            mold_number VARCHAR,
                            design_number VARCHAR,
                            has_waterproof BOOLEAN DEFAULT 0,
                            has_film BOOLEAN DEFAULT 0,
                            category VARCHAR,
                            finished_size VARCHAR,
                            material_weight INTEGER,
                            material_code VARCHAR,
                            paper_width FLOAT,
                            cut_width FLOAT,
                            cut_length FLOAT,
                            small_sheets INTEGER,
                            large_sheets INTEGER,
                            cutting_sheets INTEGER,
                            corrugated VARCHAR,
                            pressing_lines VARCHAR,
                            meters FLOAT,
                            area FLOAT,
                            cardboard_unit_price FLOAT,
                            subtotal_before_tax FLOAT,
                            moq INTEGER,
                            labor_cost FLOAT,
                            shipping_fee FLOAT,
                            film_cost FLOAT,
                            mold_cost FLOAT,
                            additional_cost FLOAT,
                            total_cost FLOAT,
                            unit_price VARCHAR DEFAULT 'pcs',
                            profit_amount FLOAT,
                            profit_percentage FLOAT,
                            description TEXT,
                            is_active BOOLEAN DEFAULT 1,
                            created_by VARCHAR,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                        )
                    """))
                    
                    # 複製資料（排除 length_d, width_r, height_c）
                    connection.execute(text("""
                        INSERT INTO products_new (
                            id, quote_date, supplier_code, customer_name, product_code, product_name,
                            mold_number, design_number, has_waterproof, has_film, category, finished_size,
                            material_weight, material_code, paper_width, cut_width, cut_length,
                            small_sheets, large_sheets, cutting_sheets, corrugated, pressing_lines,
                            meters, area, cardboard_unit_price, subtotal_before_tax, moq,
                            labor_cost, shipping_fee, film_cost, mold_cost, additional_cost,
                            total_cost, unit_price, profit_amount, profit_percentage, description,
                            is_active, created_by, created_at, updated_at
                        )
                        SELECT 
                            id, quote_date, supplier_code, customer_name, product_code, product_name,
                            mold_number, design_number, has_waterproof, has_film, category, finished_size,
                            material_weight, material_code, paper_width, cut_width, cut_length,
                            small_sheets, large_sheets, cutting_sheets, corrugated, pressing_lines,
                            meters, area, cardboard_unit_price, subtotal_before_tax, moq,
                            labor_cost, shipping_fee, film_cost, mold_cost, additional_cost,
                            total_cost, unit_price, profit_amount, profit_percentage, description,
                            is_active, created_by, created_at, updated_at
                        FROM products
                    """))
                    
                    # 刪除舊表格，重命名新表格
                    connection.execute(text("DROP TABLE products"))
                    connection.execute(text("ALTER TABLE products_new RENAME TO products"))
                    
                    # 2. 重建 cost_analysis 表格
                    print("重建 cost_analysis 表格...")
                    connection.execute(text("""
                        CREATE TABLE cost_analysis_new (
                            id INTEGER PRIMARY KEY,
                            quote_date DATE,
                            supplier_code VARCHAR,
                            customer_name VARCHAR,
                            product_code VARCHAR,
                            product_name VARCHAR,
                            mold_number VARCHAR,
                            design_number VARCHAR,
                            has_waterproof BOOLEAN DEFAULT 0,
                            has_film BOOLEAN DEFAULT 0,
                            category VARCHAR,
                            specifications VARCHAR,
                            paper_quality INTEGER,
                            paper_code VARCHAR,
                            paper_width FLOAT,
                            cut_width FLOAT,
                            cut_length FLOAT,
                            small_sheets INTEGER,
                            large_sheets INTEGER,
                            cutting_sheets INTEGER,
                            corrugated VARCHAR,
                            pressing_lines VARCHAR,
                            meters FLOAT,
                            area FLOAT,
                            cardboard_unit_price FLOAT,
                            subtotal_before_tax FLOAT,
                            moq INTEGER,
                            labor_cost FLOAT,
                            shipping_fee FLOAT,
                            film_cost FLOAT,
                            mold_cost FLOAT,
                            additional_cost FLOAT,
                            total_cost FLOAT,
                            unit_price FLOAT,
                            profit_amount FLOAT,
                            profit_percentage FLOAT,
                            status VARCHAR DEFAULT 'draft',
                            notes TEXT,
                            created_by VARCHAR,
                            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                        )
                    """))
                    
                    # 複製資料（排除 length_d, width_r, height_c）
                    connection.execute(text("""
                        INSERT INTO cost_analysis_new (
                            id, quote_date, supplier_code, customer_name, product_code, product_name,
                            mold_number, design_number, has_waterproof, has_film, category, specifications,
                            paper_quality, paper_code, paper_width, cut_width, cut_length,
                            small_sheets, large_sheets, cutting_sheets, corrugated, pressing_lines,
                            meters, area, cardboard_unit_price, subtotal_before_tax, moq,
                            labor_cost, shipping_fee, film_cost, mold_cost, additional_cost,
                            total_cost, unit_price, profit_amount, profit_percentage, status,
                            notes, created_by, created_at, updated_at
                        )
                        SELECT 
                            id, quote_date, supplier_code, customer_name, product_code, product_name,
                            mold_number, design_number, has_waterproof, has_film, category, specifications,
                            paper_quality, paper_code, paper_width, cut_width, cut_length,
                            small_sheets, large_sheets, cutting_sheets, corrugated, pressing_lines,
                            meters, area, cardboard_unit_price, subtotal_before_tax, moq,
                            labor_cost, shipping_fee, film_cost, mold_cost, additional_cost,
                            total_cost, unit_price, profit_amount, profit_percentage, status,
                            notes, created_by, created_at, updated_at
                        FROM cost_analysis
                    """))
                    
                    # 刪除舊表格，重命名新表格
                    connection.execute(text("DROP TABLE cost_analysis"))
                    connection.execute(text("ALTER TABLE cost_analysis_new RENAME TO cost_analysis"))
                    
                else:
                    # 對於其他資料庫（如 PostgreSQL, MySQL），直接刪除欄位
                    print("使用 ALTER TABLE DROP COLUMN 方式...")
                    
                    # 刪除 products 表格的欄位
                    connection.execute(text("ALTER TABLE products DROP COLUMN length_d"))
                    connection.execute(text("ALTER TABLE products DROP COLUMN width_r"))
                    connection.execute(text("ALTER TABLE products DROP COLUMN height_c"))
                    
                    # 刪除 cost_analysis 表格的欄位
                    connection.execute(text("ALTER TABLE cost_analysis DROP COLUMN length_d"))
                    connection.execute(text("ALTER TABLE cost_analysis DROP COLUMN width_r"))
                    connection.execute(text("ALTER TABLE cost_analysis DROP COLUMN height_c"))
                
                # 提交事務
                trans.commit()
                print("✅ 成功刪除長/D、寬/R、高/C三個尺寸欄位")
                
            except Exception as e:
                # 回滾事務
                trans.rollback()
                print(f"❌ 遷移失敗，已回滾: {e}")
                raise
                
    except Exception as e:
        print(f"❌ 資料庫連接失敗: {e}")
        raise

if __name__ == "__main__":
    print("開始執行資料庫遷移：刪除尺寸欄位")
    print("=" * 50)
    
    try:
        remove_dimension_fields()
        print("\n" + "=" * 50)
        print("遷移完成！")
    except Exception as e:
        print(f"\n遷移失敗: {e}")
        exit(1)
