{"name": "react-to-print", "version": "3.1.1", "description": "Print React components in the browser", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "scripts": {"build": "NODE_ENV=production webpack --progress", "lint": "eslint ./src", "prepare": "npm run lint && npm run build && husky install", "start": "NODE_ENV=development webpack serve"}, "repository": {"type": "git", "url": "git+https://github.com/MatthewHerbst/react-to-print.git"}, "keywords": ["react", "print", "reactjs", "react-to-print"], "author": "<PERSON> <MatthewHerbst.com>", "contributors": ["gregnb <<EMAIL>>"], "license": "MIT", "bugs": {"url": "https://github.com/MatthewHerbst/react-to-print/issues"}, "homepage": "https://github.com/MatthewHerbst/react-to-print#readme", "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ~19"}, "devDependencies": {"@eslint/js": "^9.30.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^13.0.0", "css-loader": "^7.1.2", "eslint": "^9.30.0", "eslint-plugin-react": "^7.37.5", "html-webpack-plugin": "^5.6.3", "husky": "^9.1.7", "lint-staged": "^16.1.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-tabs": "^6.1.0", "style-loader": "^4.0.0", "ts-loader": "^9.5.2", "tslib": "^2.8.1", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "url-loader": "^4.1.1", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2"}, "lint-staged": {"*.{ts,tsx}": ["eslint"]}}