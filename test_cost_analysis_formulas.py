#!/usr/bin/env python3
"""
測試成本分析計算公式的腳本
驗證各種計算公式是否正確
"""

import math

def test_formulas():
    """測試所有計算公式"""
    print("成本分析計算公式測試")
    print("=" * 50)
    
    # 測試資料
    test_data = {
        "paper_width": 1200,      # 門幅 (mm)
        "cut_width": 300,         # 切寬 (mm)
        "cut_length": 400,        # 切長 (mm)
        "small_sheets": 1000,     # 小張數
        "cardboard_unit_price": 20000,  # 紙板單價 (VND/m²)
        "has_waterproof": True,   # 防水選項
        "labor_cost": 50000,      # 人工成本
        "shipping_fee": 30000,    # 運輸費
        "mold_cost": 100000,      # 刀模費
        "additional_cost": 20000, # 額外開支
        "unit_price": 500000      # 單價
    }
    
    print("輸入資料:")
    for key, value in test_data.items():
        print(f"  {key}: {value}")
    
    print("\n計算結果:")
    print("-" * 30)
    
    # 1. 開張數 = ROUNDDOWN(門幅/切寬, 0)
    cutting_sheets = math.floor(test_data["paper_width"] / test_data["cut_width"])
    print(f"1. 開張數 = ROUNDDOWN({test_data['paper_width']}/{test_data['cut_width']}, 0) = {cutting_sheets}")
    
    # 2. 大張數 = 小張數/開張數
    large_sheets = test_data["small_sheets"] / cutting_sheets
    print(f"2. 大張數 = {test_data['small_sheets']}/{cutting_sheets} = {large_sheets:.2f}")
    
    # 3. 米數 = 切長/1000*大張數
    meters = (test_data["cut_length"] / 1000) * large_sheets
    print(f"3. 米數 = {test_data['cut_length']}/1000*{large_sheets:.2f} = {meters:.2f} m")
    
    # 4. 平方數 = 切長/1000*門幅/1000*大張數
    area = (test_data["cut_length"] / 1000) * (test_data["paper_width"] / 1000) * large_sheets
    print(f"4. 平方數 = {test_data['cut_length']}/1000*{test_data['paper_width']}/1000*{large_sheets:.2f} = {area:.2f} m²")
    
    # 5. 小計 = 平方數*紙板單價
    subtotal = area * test_data["cardboard_unit_price"]
    print(f"5. 小計 = {area:.2f}*{test_data['cardboard_unit_price']} = ₫{subtotal:,.0f}")
    
    # 6. MOQ = 300000/切長*開張數
    moq = (300000 / test_data["cut_length"]) * cutting_sheets
    print(f"6. MOQ = 300000/{test_data['cut_length']}*{cutting_sheets} = {moq:.0f}")
    
    # 7. 過膜 = IF(防水選項="打開", 平方數*1000, IF(防水選項="關閉", 0, ""))
    film_cost = area * 1000 if test_data["has_waterproof"] else 0
    waterproof_status = "打開" if test_data["has_waterproof"] else "關閉"
    print(f"7. 過膜 = IF(防水選項=\"{waterproof_status}\", {area:.2f}*1000, 0) = ₫{film_cost:,.0f}")
    
    # 8. 總成本 = 小計+人工成本+運輸費+過膜+刀模費+額外開支
    total_cost = (subtotal + 
                  test_data["labor_cost"] + 
                  test_data["shipping_fee"] + 
                  film_cost + 
                  test_data["mold_cost"] + 
                  test_data["additional_cost"])
    print(f"8. 總成本 = {subtotal:,.0f}+{test_data['labor_cost']:,}+{test_data['shipping_fee']:,}+{film_cost:,.0f}+{test_data['mold_cost']:,}+{test_data['additional_cost']:,}")
    print(f"         = ₫{total_cost:,.0f}")
    
    # 9. 利潤 = 單價-總成本
    profit_amount = test_data["unit_price"] - total_cost
    print(f"9. 利潤 = {test_data['unit_price']:,}-{total_cost:,.0f} = ₫{profit_amount:,.0f}")
    
    # 10. 利潤% = 利潤/總成本 * 100
    profit_percentage = (profit_amount / total_cost) * 100
    print(f"10. 利潤% = {profit_amount:,.0f}/{total_cost:,.0f}*100 = {profit_percentage:.2f}%")
    
    print("\n" + "=" * 50)
    
    # 測試邊界情況
    print("邊界情況測試:")
    print("-" * 20)
    
    # 測試防水關閉的情況
    print("1. 防水關閉時:")
    film_cost_off = 0
    total_cost_off = (subtotal + 
                      test_data["labor_cost"] + 
                      test_data["shipping_fee"] + 
                      film_cost_off + 
                      test_data["mold_cost"] + 
                      test_data["additional_cost"])
    profit_amount_off = test_data["unit_price"] - total_cost_off
    profit_percentage_off = (profit_amount_off / total_cost_off) * 100
    print(f"   過膜費用: ₫{film_cost_off:,}")
    print(f"   總成本: ₫{total_cost_off:,.0f}")
    print(f"   利潤: ₫{profit_amount_off:,.0f}")
    print(f"   利潤%: {profit_percentage_off:.2f}%")
    
    # 測試不同門幅的情況
    print("\n2. 不同門幅測試:")
    test_widths = [1000, 1200, 1500, 2000]
    for width in test_widths:
        cutting_sheets_test = math.floor(width / test_data["cut_width"])
        large_sheets_test = test_data["small_sheets"] / cutting_sheets_test
        moq_test = (300000 / test_data["cut_length"]) * cutting_sheets_test
        print(f"   門幅{width}mm: 開張數={cutting_sheets_test}, 大張數={large_sheets_test:.2f}, MOQ={moq_test:.0f}")

def validate_corrugated_types():
    """驗證瓦楞類型選項"""
    print("\n瓦楞類型驗證:")
    print("-" * 20)
    
    expected_types = ["C", "B", "E", "CB", "EB"]
    print(f"期望的瓦楞類型: {expected_types}")
    print("✅ 瓦楞類型選項已更新為5種固定選項")

def test_calculation_precision():
    """測試計算精度"""
    print("\n計算精度測試:")
    print("-" * 20)
    
    # 測試小數精度
    test_cases = [
        {"paper_width": 1234, "cut_width": 567, "cut_length": 890, "small_sheets": 1000},
        {"paper_width": 999, "cut_width": 333, "cut_length": 777, "small_sheets": 2000},
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"測試案例 {i}:")
        cutting_sheets = math.floor(case["paper_width"] / case["cut_width"])
        large_sheets = case["small_sheets"] / cutting_sheets
        meters = (case["cut_length"] / 1000) * large_sheets
        area = (case["cut_length"] / 1000) * (case["paper_width"] / 1000) * large_sheets
        
        print(f"  開張數: {cutting_sheets}")
        print(f"  大張數: {large_sheets:.2f}")
        print(f"  米數: {meters:.2f} m")
        print(f"  平方數: {area:.2f} m²")
        print()

if __name__ == "__main__":
    test_formulas()
    validate_corrugated_types()
    test_calculation_precision()
    
    print("\n使用說明:")
    print("1. 在成本分析頁面輸入基本資料（門幅、切寬、切長、小張數等）")
    print("2. 系統會自動計算開張數、大張數、米數、平方數")
    print("3. 輸入紙板單價後會自動計算小計")
    print("4. 切換防水選項會自動計算過膜費用")
    print("5. 輸入各項成本後會自動計算總成本")
    print("6. 輸入單價後會自動計算利潤和利潤百分比")
    print("7. 所有計算結果會即時更新並顯示在對應欄位中")
