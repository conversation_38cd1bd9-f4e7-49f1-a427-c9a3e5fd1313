import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Modal,
  Form,
  message,
  Popconfirm,
  Row,
  Col,
  Typography,
  Tag,
  DatePicker,
  InputNumber,
  Statistic,
  Switch,
  Checkbox
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  Bar<PERSON>hartOutlined,
  DollarOutlined,
  ShopOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs from 'dayjs';
import { productDataAPI } from '../services/productDataAPI';

// 定義類型介面
interface Product {
  id: number;
  quote_date?: string;
  supplier_code?: string;
  customer_name?: string;
  product_code: string;
  product_name?: string;
  mold_number?: string;
  design_number?: string;
  has_waterproof: boolean;
  has_film: boolean;
  category?: string;
  finished_size?: string;
  material_weight?: number;
  material_code?: string;
  paper_width?: number;
  cut_width?: number;
  cut_length?: number;
  small_sheets?: number;
  large_sheets?: number;
  cutting_sheets?: number;
  corrugated?: string;
  pressing_lines?: string;
  meters?: number;
  area?: number;
  cardboard_unit_price?: number;
  subtotal_before_tax?: number;
  moq?: number;
  labor_cost?: number;
  shipping_fee?: number;
  film_cost?: number;
  mold_cost?: number;
  additional_cost?: number;
  total_cost?: number;
  unit_price: string;
  profit_amount?: number;
  profit_percentage?: number;
  description?: string;
  is_active: boolean;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

interface ProductSummary {
  total_products: number;
  active_products: number;
  inactive_products: number;
  category_count: number;
  supplier_count: number;
  average_profit_rate: number;
}

const { Title, Text } = Typography;
const { TextArea } = Input;

const ProductDataPage: React.FC = () => {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  // 狀態管理
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
  const [categories, setCategories] = useState<string[]>([]);
  const [suppliers, setSuppliers] = useState<string[]>([]);
  const [customers, setCustomers] = useState<string[]>([]);
  const [summary, setSummary] = useState<ProductSummary | null>(null);
  
  // 搜尋和篩選
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  const [supplierFilter, setSupplierFilter] = useState<string>('');
  const [customerFilter, setCustomerFilter] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');

  // 分頁狀態
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
    showSizeChanger: true,
    showQuickJumper: true,
    pageSizeOptions: ['10', '20', '50', '100'],
    showTotal: (total: number, range: [number, number]) =>
      `第 ${range[0]}-${range[1]} 筆，共 ${total} 筆`,
  });

  // 獲取產品列表
  const fetchProducts = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const params = {
        page,
        pageSize,
        ...(searchTerm && { search: searchTerm }),
        ...(categoryFilter && { category: categoryFilter }),
        ...(supplierFilter && { supplier_code: supplierFilter }),
        ...(customerFilter && { customer_name: customerFilter }),
        ...(statusFilter && { is_active: statusFilter === 'active' }),
      };

      const response = await productDataAPI.getProducts(params);
      const { data, total, page: currentPage, pageSize: currentPageSize } = response.data;

      setProducts(data);
      setPagination(prev => ({
        ...prev,
        current: currentPage,
        pageSize: currentPageSize,
        total,
      }));
    } catch (error) {
      console.error('Failed to fetch products:', error);
      message.error('獲取商品資料失敗');
    } finally {
      setLoading(false);
    }
  };

  // 獲取統計資料和元數據
  const fetchMetadata = async () => {
    try {
      const [categoriesRes, suppliersRes, customersRes, summaryRes] = await Promise.all([
        productDataAPI.getCategoriesList(),
        productDataAPI.getSuppliersList(),
        productDataAPI.getCustomersList(),
        productDataAPI.getProductSummary()
      ]);
      setCategories(categoriesRes.data);
      setSuppliers(suppliersRes.data);
      setCustomers(customersRes.data);
      setSummary(summaryRes.data);
    } catch (error) {
      console.error('Failed to fetch metadata:', error);
    }
  };

  // 初始化數據
  useEffect(() => {
    fetchProducts();
    fetchMetadata();
  }, []);

  // 搜尋處理
  const handleSearch = () => {
    fetchProducts(1, pagination.pageSize);
  };

  // 重置搜尋
  const handleReset = () => {
    setSearchTerm('');
    setCategoryFilter('');
    setSupplierFilter('');
    setCustomerFilter('');
    setStatusFilter('');
    setTimeout(() => {
      fetchProducts(1, pagination.pageSize);
    }, 100);
  };

  // 分頁變更處理
  const handleTableChange = (paginationConfig: any) => {
    fetchProducts(paginationConfig.current, paginationConfig.pageSize);
  };

  // 顯示新增表單
  const showAddForm = () => {
    setEditMode(false);
    setCurrentProduct(null);
    form.resetFields();
    form.setFieldsValue({
      unit_price: 'pcs',
      is_active: true,
      has_waterproof: false,
      has_film: false,
      quote_date: dayjs()
    });
    setModalVisible(true);
  };

  // 顯示編輯表單
  const showEditForm = (product: Product) => {
    setEditMode(true);
    setCurrentProduct(product);
    form.setFieldsValue({
      ...product,
      quote_date: product.quote_date ? dayjs(product.quote_date) : null,
    });
    setModalVisible(true);
  };

  // 表單提交處理
  const handleFormSubmit = async (values: any) => {
    setLoading(true);
    try {
      const productData = {
        ...values,
        quote_date: values.quote_date?.format('YYYY-MM-DD'),
      };

      if (editMode && currentProduct) {
        // 更新商品
        await productDataAPI.updateProduct(currentProduct.id, productData);
        message.success('商品更新成功');
      } else {
        // 新增商品
        await productDataAPI.createProduct(productData);
        message.success('商品新增成功');
      }
      
      setModalVisible(false);
      form.resetFields();
      fetchProducts(pagination.current, pagination.pageSize);
      fetchMetadata(); // 重新獲取統計資料
    } catch (error: any) {
      console.error('Failed to save product:', error);
      const errorMessage = error.response?.data?.detail || '操作失敗';
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 刪除商品
  const handleDelete = async (product: Product) => {
    try {
      await productDataAPI.deleteProduct(product.id);
      message.success('商品已刪除');
      fetchProducts(pagination.current, pagination.pageSize);
      fetchMetadata();
    } catch (error) {
      console.error('Failed to delete product:', error);
      message.error('刪除失敗');
    }
  };

  // 表格欄位定義 - 包含所有您要求的欄位
  const columns = [
    {
      title: '報價時間 / Ngày báo giá',
      dataIndex: 'quote_date',
      key: 'quote_date',
      width: 120,
      render: (date: string) => date ? dayjs(date).format('YYYY/MM/DD') : '-',
    },
    {
      title: '供應商代碼 / Mã nhà cung cấp',
      dataIndex: 'supplier_code',
      key: 'supplier_code',
      width: 150,
    },
    {
      title: '客戶 / Khách hàng',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 120,
    },
    {
      title: '編號/料號 / Mã hàng',
      dataIndex: 'product_code',
      key: 'product_code',
      width: 150,
      fixed: 'left' as const,
    },
    {
      title: '品名 / Tên hàng',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 200,
      ellipsis: true,
    },
    {
      title: '刀模號 / số khuôn',
      dataIndex: 'mold_number',
      key: 'mold_number',
      width: 120,
    },
    {
      title: '圖稿 / số bản in',
      dataIndex: 'design_number',
      key: 'design_number',
      width: 120,
    },
    {
      title: '防水 / chống thấm',
      dataIndex: 'has_waterproof',
      key: 'has_waterproof',
      width: 100,
      render: (hasWaterproof: boolean) => hasWaterproof ? 'O' : ' ',
    },
    {
      title: '過模 / qua màng',
      dataIndex: 'has_film',
      key: 'has_film',
      width: 100,
      render: (hasFilm: boolean) => hasFilm ? 'O' : ' ',
    },
    {
      title: '分類 / Loại',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      render: (category: string) => category ? <Tag color="blue">{category}</Tag> : '-',
    },
    {
      title: '成品規格 / Quy cách (mm)',
      dataIndex: 'finished_size',
      key: 'finished_size',
      width: 150,
      ellipsis: true,
    },

    {
      title: '材質克重 / chất lượng giấy (gram)',
      dataIndex: 'material_weight',
      key: 'material_weight',
      width: 140,
      render: (weight: number) => weight ? `${weight}g` : '-',
    },
    {
      title: '材質代号 / mã giấy',
      dataIndex: 'material_code',
      key: 'material_code',
      width: 120,
    },
    {
      title: '门幅 / KHỔ',
      dataIndex: 'paper_width',
      key: 'paper_width',
      width: 100,
    },
    {
      title: '切宽 / CẮT RỘNG (mm)',
      dataIndex: 'cut_width',
      key: 'cut_width',
      width: 120,
    },
    {
      title: '切长 / CẮT DÀI (mm)',
      dataIndex: 'cut_length',
      key: 'cut_length',
      width: 120,
    },
    {
      title: '小张数 / SỐ TẤM NHỎ',
      dataIndex: 'small_sheets',
      key: 'small_sheets',
      width: 120,
    },
    {
      title: '大张数 / SỐ TẤM LỚN',
      dataIndex: 'large_sheets',
      key: 'large_sheets',
      width: 120,
    },
    {
      title: '开张数 / SỐ DAO',
      dataIndex: 'cutting_sheets',
      key: 'cutting_sheets',
      width: 120,
    },
    {
      title: '瓦楞 / SÓNG',
      dataIndex: 'corrugated',
      key: 'corrugated',
      width: 100,
    },
    {
      title: '压线 / CÁN LẰN',
      dataIndex: 'pressing_lines',
      key: 'pressing_lines',
      width: 100,
    },
    {
      title: '米数 / SỐ MÉT (m)',
      dataIndex: 'meters',
      key: 'meters',
      width: 120,
    },
    {
      title: '平方数 / DIỆN TÍCH (m²)',
      dataIndex: 'area',
      key: 'area',
      width: 140,
    },
    {
      title: '紙板單價 / Đơn Giá vnd/㎡',
      dataIndex: 'cardboard_unit_price',
      key: 'cardboard_unit_price',
      width: 150,
      render: (price: number) => price ? `₫${price.toLocaleString()}` : '-',
    },
    {
      title: '小计 / (chưa tính thuế)',
      dataIndex: 'subtotal_before_tax',
      key: 'subtotal_before_tax',
      width: 140,
      render: (amount: number) => amount ? `₫${amount.toLocaleString()}` : '-',
    },
    {
      title: 'MOQ',
      dataIndex: 'moq',
      key: 'moq',
      width: 80,
    },
    {
      title: '人工成本 / Chi phí nhân công',
      dataIndex: 'labor_cost',
      key: 'labor_cost',
      width: 150,
      render: (cost: number) => cost ? `₫${cost.toLocaleString()}` : '-',
    },
    {
      title: '运输费 / Phí vận chuyển',
      dataIndex: 'shipping_fee',
      key: 'shipping_fee',
      width: 140,
      render: (fee: number) => fee ? `₫${fee.toLocaleString()}` : '-',
    },
    {
      title: '過模費 / qua màng',
      dataIndex: 'film_cost',
      key: 'film_cost',
      width: 120,
      render: (cost: number) => cost ? `₫${cost.toLocaleString()}` : '-',
    },
    {
      title: '刀模費 / tiền khuôn',
      dataIndex: 'mold_cost',
      key: 'mold_cost',
      width: 120,
      render: (cost: number) => cost ? `₫${cost.toLocaleString()}` : '-',
    },
    {
      title: '额外开支 / Chi phí phát sinh',
      dataIndex: 'additional_cost',
      key: 'additional_cost',
      width: 150,
      render: (cost: number) => cost ? `₫${cost.toLocaleString()}` : '-',
    },
    {
      title: '總成本 / Tổng chi phí',
      dataIndex: 'total_cost',
      key: 'total_cost',
      width: 140,
      render: (cost: number) => cost ? `₫${cost.toLocaleString()}` : '-',
    },
    {
      title: '單價 / Đơn giá',
      dataIndex: 'unit_price',
      key: 'unit_price',
      width: 100,
    },
    {
      title: '利润 / Lợi nhuận',
      dataIndex: 'profit_amount',
      key: 'profit_amount',
      width: 120,
      render: (profit: number) => profit ? `₫${profit.toLocaleString()}` : '-',
    },
    {
      title: '利润率 / Lợi nhuận (%)',
      dataIndex: 'profit_percentage',
      key: 'profit_percentage',
      width: 120,
      render: (percentage: number) => percentage ? `${percentage.toFixed(2)}%` : '-',
    },
    {
      title: '狀態',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 80,
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '啟用' : '停用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right' as const,
      render: (_: any, record: Product) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => showEditForm(record)}
          >
            編輯
          </Button>
          <Popconfirm
            title="確定要刪除這個商品嗎？"
            onConfirm={() => handleDelete(record)}
            okText="確定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              刪除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 統計卡片 */}
      {summary && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic title="總商品" value={summary.total_products} prefix={<BarChartOutlined />} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="啟用商品" value={summary.active_products} valueStyle={{ color: '#52c41a' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="供應商數" value={summary.supplier_count} prefix={<ShopOutlined />} valueStyle={{ color: '#1890ff' }} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic 
                title="平均利潤率" 
                value={summary.average_profit_rate} 
                prefix={<DollarOutlined />}
                formatter={(value) => `${Number(value).toFixed(2)}%`}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={2}>商品資料管理</Title>
        </div>

        {/* 搜尋和篩選區域 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={5}>
            <Input
              placeholder="搜尋商品代碼、品名或刀模號"
              prefix={<SearchOutlined />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onPressEnter={handleSearch}
            />
          </Col>
          <Col span={3}>
            <Select
              placeholder="分類"
              style={{ width: '100%' }}
              allowClear
              value={categoryFilter || undefined}
              onChange={(value) => setCategoryFilter(value || '')}
            >
              {categories.map(category => (
                <Select.Option key={category} value={category}>{category}</Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="供應商"
              style={{ width: '100%' }}
              allowClear
              value={supplierFilter || undefined}
              onChange={(value) => setSupplierFilter(value || '')}
            >
              {suppliers.map(supplier => (
                <Select.Option key={supplier} value={supplier}>{supplier}</Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="客戶"
              style={{ width: '100%' }}
              allowClear
              value={customerFilter || undefined}
              onChange={(value) => setCustomerFilter(value || '')}
            >
              {customers.map(customer => (
                <Select.Option key={customer} value={customer}>{customer}</Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={3}>
            <Select
              placeholder="狀態"
              style={{ width: '100%' }}
              allowClear
              value={statusFilter || undefined}
              onChange={(value) => setStatusFilter(value || '')}
            >
              <Select.Option value="active">啟用</Select.Option>
              <Select.Option value="inactive">停用</Select.Option>
            </Select>
          </Col>
          <Col span={7}>
            <Space>
              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜尋
              </Button>
              <Button icon={<ReloadOutlined />} onClick={handleReset}>
                重置
              </Button>
              <Button type="primary" icon={<PlusOutlined />} onClick={showAddForm}>
                新增商品
              </Button>
            </Space>
          </Col>
        </Row>

        {/* 表格 */}
        <Table
          columns={columns}
          dataSource={products}
          rowKey="id"
          loading={loading}
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 4000 }}
          size="small"
        />
      </Card>
    </div>
  );
};

export default ProductDataPage;
