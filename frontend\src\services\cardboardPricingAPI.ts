import api from './api';

export interface CardboardPricing {
  id: number;
  date?: string;
  supplier_code?: string;
  material_weight?: number;
  material_code?: string;
  unit_price?: number;
  corrugated_type?: string;
  currency: string;
  status: string;
  effective_date?: string;
  expiry_date?: string;
  notes?: string;
  created_by?: string;
  created_at: string;
  updated_at: string;
}

export interface CardboardPricingCreate {
  date?: string;
  supplier_code?: string;
  material_weight?: number;
  material_code?: string;
  unit_price?: number;
  corrugated_type?: string;
  currency?: string;
  status?: string;
  effective_date?: string;
  expiry_date?: string;
  notes?: string;
  created_by?: string;
}

export interface CardboardPricingUpdate extends Partial<CardboardPricingCreate> {}

export interface PaginatedCardboardPricingResponse {
  data: CardboardPricing[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export interface CardboardPricingQueryParams {
  page?: number;
  pageSize?: number;
  search?: string;
  supplier_code?: string;
  material_code?: string;
  corrugated_type?: string;
  status?: string;
  date_from?: string;
  date_to?: string;
}

export interface PricingSummary {
  total_records: number;
  active_records: number;
  inactive_records: number;
  supplier_count: number;
  material_count: number;
  average_price: number;
}

export interface SupplierOption {
  id: number;
  supplier_code: string;
  company_name: string;
  contact_person: string;
  display_name: string;
}

export interface MaterialOption {
  id: number;
  material_code: string;
  material_name: string;
  material_weight: number;
  display_name: string;
}

export interface CorrugatedOption {
  id: number;
  corrugated_code: string;
  corrugated_name: string;
  display_name: string;
}

class CardboardPricingAPI {
  private baseURL = '/api/cardboard-pricing';

  async getCardboardPricing(params: CardboardPricingQueryParams = {}): Promise<{ data: PaginatedCardboardPricingResponse }> {
    const response = await api.get(this.baseURL, { params });
    return response;
  }

  async getCardboardPricingItem(id: number): Promise<{ data: CardboardPricing }> {
    const response = await api.get(`${this.baseURL}/${id}`);
    return response;
  }

  async createCardboardPricing(pricing: CardboardPricingCreate): Promise<{ data: CardboardPricing }> {
    const response = await api.post(this.baseURL, pricing);
    return response;
  }

  async updateCardboardPricing(id: number, pricing: CardboardPricingUpdate): Promise<{ data: CardboardPricing }> {
    const response = await api.put(`${this.baseURL}/${id}`, pricing);
    return response;
  }

  async deleteCardboardPricing(id: number): Promise<{ data: { message: string } }> {
    const response = await api.delete(`${this.baseURL}/${id}`);
    return response;
  }

  async getSuppliersList(): Promise<{ data: SupplierOption[] }> {
    const response = await api.get(`${this.baseURL}/suppliers/list`);
    return response;
  }

  async getMaterialsList(): Promise<{ data: MaterialOption[] }> {
    const response = await api.get(`${this.baseURL}/materials/list`);
    return response;
  }

  async getCorrugatedTypesList(): Promise<{ data: CorrugatedOption[] }> {
    const response = await api.get(`${this.baseURL}/corrugated-types/list`);
    return response;
  }

  async getPricingSummary(): Promise<{ data: PricingSummary }> {
    const response = await api.get(`${this.baseURL}/dashboard/summary`);
    return response;
  }

  async batchUpdateStatus(pricingIds: number[], newStatus: string): Promise<{ data: { message: string } }> {
    const response = await api.post(`${this.baseURL}/batch-update-status`, {
      pricing_ids: pricingIds,
      new_status: newStatus
    });
    return response;
  }

  async searchByMaterial(materialCode: string, supplierCode?: string, corrugatedType?: string): Promise<{ data: CardboardPricing[] }> {
    const params = {
      material_code: materialCode,
      ...(supplierCode && { supplier_code: supplierCode }),
      ...(corrugatedType && { corrugated_type: corrugatedType })
    };
    const response = await api.get(`${this.baseURL}/search/by-material`, { params });
    return response;
  }

  async searchByWeight(materialWeight: number, supplierCode?: string, corrugatedType?: string): Promise<{ data: CardboardPricing[] }> {
    const params = {
      material_weight: materialWeight,
      ...(supplierCode && { supplier_code: supplierCode }),
      ...(corrugatedType && { corrugated_type: corrugatedType })
    };
    const response = await api.get(`${this.baseURL}/search/by-weight`, { params });
    return response;
  }
}

export const cardboardPricingAPI = new CardboardPricingAPI();
